# !/usr/bin/env python
# -*- coding:utf-8 -*-
# region ============================================备注说明
"""
    套利资金费，卖出合约，买入合约。
    WebSocket版本：使用WebSocket获取实时行情，REST API下单
    特性：
    - 实时行情推送，无延迟
    - WebSocket自动重连机制
    - 事件驱动的交易逻辑
    - 内存缓存最新行情数据
"""
# endregion ============================================备注说明


# region ============================================import
import asyncio
from time import sleep
import sys
from traceback import format_exc

from kzmfunction.ExchangeAdapter.exchangeadapter.BinanceAdapter import BinanceAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BybitAdapter import BybitAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.VertexAdapter import VertexAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.ApexproAdapter import ApexproAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.GateioAdapter import GateioAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.HyperliquidAdapter import HyperliquidAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.BitgetAdapter import BitgetAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.OkxAdapter import OkxAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BackpackAdapter import BackpackAdapter
from kzmfunction.WebSocketAdapter.WebSocketCore import (
    # OrderbookData,
    WSConfig,
    OrderbookCache,
    WebSocketManager,
    create_wss_adapter
)
from kzmfunction.WebSocketAdapter.CallbackHandlers import (
    create_callback_handler
)
from kzmfunction.WebSocketAdapter.DisplayUtils import print_red, print_green
from kzmfunction.function import sendSlackMsgAsync
# endregion ============================================import


# region ============================================全局变量

# endregion ============================================全局变量


# region ============================================WebSocket回调函数
def on_orderbook_message(adapter, data: dict):
    """处理订单簿消息的回调函数"""
    if callback_handler:
        callback_handler.handle_orderbook_update(adapter, data)

def get_trading_enabled():
    """获取交易状态"""
    global trading_enabled
    return trading_enabled

async def trigger_trading_logic_wrapper(exchange_1_data: dict, exchange_2_data: dict):
    """触发交易逻辑的包装函数"""
    await trigger_trading_logic()
# endregion ============================================WebSocket回调函数


# region ============================================交易逻辑函数
async def trigger_trading_logic():
    """触发交易逻辑"""
    global trading_enabled, total_amount

    try:
        # 确保total_amount已初始化
        if 'total_amount' not in globals():
            total_amount = 0
        # 获取两个交易所的最新行情数据
        exchange_1_orderbook, exchange_2_orderbook = orderbook_cache.get_both_fresh(
            exchange_1_name, exchange_2_name, ws_config.data_timeout
        )

        if not exchange_1_orderbook or not exchange_2_orderbook:
            return

        # 提取价格和数量信息 (相反的交易逻辑：exchange_1买1价格，exchange_2卖1价格)
        exchange_1_buy1_price = exchange_1_orderbook.bid_price
        exchange_1_buy1_amount = exchange_1_orderbook.bid_amount
        exchange_2_sell1_price = exchange_2_orderbook.ask_price
        exchange_2_sell1_amount = exchange_2_orderbook.ask_amount

        # 计算价差 (相反的计算：exchange_1买1 / exchange_2卖1 - 1)
        r = exchange_1_buy1_price / exchange_2_sell1_price - 1

        # 判断价差是否满足要求
        if r < r_threshold:
            print(f'{exchange_1_name}价格:{exchange_1_buy1_price:.4f}, {exchange_2_name}价格:{exchange_2_sell1_price:.4f}, 价差:{r:.4f}, {exchange_1_name}数量:{exchange_1_buy1_amount:.2f}, {exchange_2_name}数量:{exchange_2_sell1_amount:.2f}')
            print('利差小于目标阀值，不入金')
            return

        # 检查盘口数量是否足够
        if exchange_1_buy1_amount <= min_execute_amount or exchange_2_sell1_amount <= min_execute_amount:
            print(f'{exchange_1_name}价格：%.4f, {exchange_2_name}价格：%.4f, 价差：%.4f' % (exchange_1_buy1_price, exchange_2_sell1_price, r))
            print(f'{exchange_1_name}盘口数量：{exchange_1_buy1_amount}')
            print(f'{exchange_2_name}盘口数量：{exchange_2_sell1_amount}')
            print('利差大于目标阀值，但盘口数量不足，不入金')
            return

        # 计算下单价格和数量 (相反的价格调整)
        exchange_1_price = round(exchange_1_buy1_price * 0.995, exchange_1_price_precision)
        exchange_2_price = round(exchange_2_sell1_price * 1.005, exchange_2_price_precision)
        execute_amount = round(
            max(min(exchange_2_sell1_amount, exchange_1_buy1_amount, max_execute_num - total_amount) * 0.8, min_trade_amount),
            amount_precision
        )

        # 检查是否还能继续交易
        if total_amount >= max_execute_num:
            print('达到最大下单币数，完成建仓计划，退出程序')
            sendSlackMsgAsync(content=f'{report_title}:\n{symbol}下单完成！', channel='套利消息')
            trading_enabled = False
            return

        if max_execute_num - total_amount < min_trade_amount:
            print(f'交易数量不足{min_trade_amount}个，当前{exchange_2_name}不再交易')
            sendSlackMsgAsync(content=f'{report_title}:\n{symbol}交易数量不足{min_trade_amount}个，当前{exchange_2_name}不再交易！', channel='套利消息')
            trading_enabled = False
            return

        # 下单
        order_info, order_info2 = await place_order(exchange_1_price, exchange_2_price, execute_amount)
        print(f'{exchange_1_name}价格：%.4f, {exchange_2_name}价格：%.4f, 价差：%.4f' % (exchange_1_buy1_price, exchange_2_sell1_price, r))
        print('利差和盘口数量大于目标阀值，开始入金')
        print(f'交易计划:{exchange_1_name}卖出合约, 卖出价格{exchange_1_buy1_price}, {exchange_2_name}买入合约, 买入价格{exchange_2_sell1_price}, 交易数量{execute_amount}')


        if not order_info or not order_info2 or not order_info.get('order_id') or 'error' in str(order_info2):
            print('下单失败，请检查订单！！')
            print(order_info)
            print(order_info2)
            sendSlackMsgAsync(content=f'{report_title}:\n{symbol}下单失败，请检查订单！！', channel='套利消息')
            trading_enabled = False
            return
        else:
            # 计算实际下单成交溢价
            sleep(1)
            exchange_1_order_info = exchange_1.get_swap_order_info(symbol, order_info.get('order_id'))
            exchange_2_order_info = exchange_2.get_swap_order_info(symbol, order_info2.get('order_id'))
            print(exchange_1_order_info)
            print(exchange_2_order_info)
            # 计算实际下单成交溢价 (相反的计算)
            premium = float(exchange_1_order_info.get('average_price')) / float(exchange_2_order_info.get('average_price')) - 1
            print_green(f'下单预期价差：{r:.4f}，实际成交价差: {premium:.4f}, 溢价：{(premium/r-1)*100:.2f}%')
        # 更新累计成交量
        total_amount += execute_amount
        print_red(f'{symbol}进行下一轮循环, 已执行数量{total_amount}, 完成比例: {total_amount / max_execute_num * 100:.2f}%')

    except Exception as e:
        print(f"交易逻辑错误: {e}")
        print(format_exc())


async def place_order(exchange_1_price, exchange_2_price, execute_amount):
    """
    exchange_1/exchange_2下单
    :param exchange_1_price: exchange_1下单价格
    :param exchange_2_price: exchange_2下单价格
    :param execute_amount: 下单数量
    :return:
    """

    # exchange_1下单卖出合约 (相反的交易方向)
    async def place_exchange_1_order():
        """
        exchange_1下单卖出合约
        """
        if close_position:
            _order['exchange_1_order'] = exchange_1.place_swap_order(symbol=symbol, direction='sell', order_type='limit', amount=execute_amount, price=exchange_1_price, close_position=True)
        else:
            _order['exchange_1_order'] = exchange_1.place_swap_order(symbol=symbol, direction='sell', order_type='limit', amount=execute_amount, price=exchange_1_price)

    # exchange_2下单买入合约 (相反的交易方向)
    async def place_exchange_2_order():
        """
        exchange_2下单买入合约
        """
        if close_position:
            _order['exchange_2_order'] = exchange_2.place_swap_order(symbol=symbol, direction='buy', order_type='limit', amount=execute_amount, price=exchange_2_price, close_position=True)
        else:
            _order['exchange_2_order'] = exchange_2.place_swap_order(symbol=symbol, direction='buy', order_type='limit', amount=execute_amount, price=exchange_2_price)

    _order = {}
    n = 0
    for _ in range(3):
        try:
            # 并发下单
            await asyncio.gather(
                place_exchange_1_order(),
                place_exchange_2_order()
            )

            _exchange_1_order = _order.get('exchange_1_order', None)
            _exchange_2_order = _order.get('exchange_2_order', None)
        except Exception as _e:
            n += 1
            if n > 2:
                return None, None
            print(_e)
        else:
            if _exchange_1_order and _exchange_2_order:
                return _exchange_1_order, _exchange_2_order
            else:
                print(_exchange_1_order)
                print(_exchange_2_order)
                return None, None


# 监控账户余额
async def monitor_usdt_balance():
    """
    监控账户USDT余额,当余额低于阈值时发送警报
    """
    while True:
        print('风控程序：监控账户余额...')
        try:
            # 检查exchange_1或exchange_2是否为binance
            if exchange_1_name == 'binance':
                # 获取exchange_1账户余额
                exchange_1_balance = exchange_1.get_swap_account_info()['assets']
                for asset in exchange_1_balance:
                    if asset['asset'] == 'USDT':
                        exchange_1_balance = float(asset['walletBalance'])
                        break
                print(exchange_1_balance)
                
                # 检查余额是否低于阈值
                if exchange_1_balance < 0:
                    print(f'{exchange_1_name}usdt余额小于0，请补充usdt保证金！')
                    sendSlackMsgAsync(
                        content=f'{report_title}:\nusdt账户余额小于0！\n{exchange_1_name}余额:{exchange_1_balance}',
                        channel='套利消息'
                    )
                    sys.exit()
            elif exchange_2_name == 'binance':
                # 获取exchange_2账户余额
                exchange_2_balance = exchange_2.get_swap_account_info()['assets']
                for asset in exchange_2_balance:
                    if asset['asset'] == 'USDT':
                        exchange_2_balance = float(asset['walletBalance'])
                        break
                print(exchange_2_balance)
                
                # 检查余额是否低于阈值
                if exchange_2_balance < 0:
                    print(f'{exchange_2_name}usdt余额小于0，请补充usdt保证金！')
                    sendSlackMsgAsync(
                        content=f'{report_title}:\nusdt账户余额小于0！\n{exchange_2_name}余额:{exchange_2_balance}',
                        channel='套利消息'
                    )
                    sys.exit()
            # 每10秒检查一次
            await asyncio.sleep(10)
        except Exception as e:
            print(f"监控余额出错: {e}")
            await asyncio.sleep(10)


# 监控交易进度
async def monitor_trading_progress_task():
    """
    简单的交易进度监控，每5秒打印一次进度
    """
    while trading_enabled:
        try:
            current_total = globals().get('total_amount', 0)
            max_total = globals().get('max_execute_num', 0)
            current_symbol = globals().get('symbol', 'Unknown')

            if max_total > 0:
                percentage = (current_total / max_total) * 100
                remaining = max_total - current_total
                print_red(f'📊 {current_symbol} 下单进度: {current_total:.4f}/{max_total:.4f} ({percentage:.2f}%) | 剩余: {remaining:.4f}')
            else:
                print_red(f'📊 {current_symbol} 下单进度: 等待初始化...')

            # 每10秒检查一次
            await asyncio.sleep(10)
        except Exception as e:
            print(f"监控进度出错: {e}")
            await asyncio.sleep(5)
# endregion ============================================交易逻辑函数


# region ============================================WebSocket设置函数
async def setup_websockets():
    """设置WebSocket连接"""
    global callback_handler

    try:
        # 初始化回调处理器（使用反向套利逻辑）
        callback_handler = create_callback_handler(
            'reverse_arbitrage',
            # 'display',  # 显示价差数据不下单
            orderbook_cache,
            exchange_1_name=exchange_1_name,
            exchange_2_name=exchange_2_name,
            symbol=symbol,
            r_threshold=r_threshold,
            trading_enabled_callback=get_trading_enabled,
            trigger_trading_callback=trigger_trading_logic_wrapper,
            trade_cooldown=trade_cooldown
        )

        # 创建exchange_1的WebSocket适配器
        exchange_1_adapter = create_wss_adapter(exchange_1_name, symbol, on_orderbook_message)
        await ws_manager.add_adapter(exchange_1_name, exchange_1_adapter)

        # 创建exchange_2的WebSocket适配器
        exchange_2_adapter = create_wss_adapter(exchange_2_name, symbol, on_orderbook_message)
        await ws_manager.add_adapter(exchange_2_name, exchange_2_adapter)

        print(f"WebSocket adapters created for {exchange_1_name} and {exchange_2_name}")

    except Exception as e:
        print(f"Error setting up WebSockets: {e}")
        raise
# endregion ============================================WebSocket设置函数


# region ============================================主程序逻辑
async def main():
    """主程序逻辑"""
    global total_amount, max_execute_num, trading_enabled

    try:
        # 初始化持仓或开仓数量
        if close_position:
            # 查看持仓
            exchange_1_max_execute_num = float(exchange_1.get_swap_position(symbol)[0]['amount'])
            exchange_2_max_execute_num = float(exchange_2.get_swap_position(symbol)[0]['amount'])
            if all([exchange_1_max_execute_num, exchange_2_max_execute_num]):
                max_execute_num = max(exchange_1_max_execute_num, exchange_2_max_execute_num) if exchange_1_max_execute_num < 0.0001 else min(exchange_2_max_execute_num, exchange_1_max_execute_num)
            else:
                print('当前账户没有持仓，退出程序！')
                sys.exit()
            print('当前账户持仓数量：', max_execute_num)
        else:
            # 使用全局变量max_execute_num
            max_execute_num = usdt_balance / symbol_price  # 重新计算最大开仓数量
            print('当前账户最大开仓数量：', max_execute_num)

        total_amount = 0  # 初始化总交易数量
        trading_enabled = True

        # 设置WebSocket连接
        await setup_websockets()

        # 启动WebSocket连接
        await ws_manager.start_all()

        print("WebSocket connections started. Trading logic will be triggered by real-time data.")
        print("Press Ctrl+C to stop the program.")

        # 保持程序运行，等待WebSocket数据触发交易
        while trading_enabled:
            await asyncio.sleep(1)

        print("Trading completed or stopped.")

    except KeyboardInterrupt:
        print("Program interrupted by user.")
    except Exception as e:
        print(f"Main program error: {e}")
        print(format_exc())
    finally:
        # 停止WebSocket连接
        await ws_manager.stop_all()


async def run():
    """运行程序"""
    try:
        # 检查持仓限制
        if exchange_2_name == 'hyperliquid':
            black_list = exchange_2.get_swap_contract_at_open_interest_cap()
            if black_list:
                if symbol.upper() in black_list:
                    print(f'{symbol}在持仓限制名单中，退出程序！')
                    sys.exit()
        elif exchange_1_name == 'hyperliquid':
            black_list = exchange_1.get_swap_contract_at_open_interest_cap()
            if black_list:
                if symbol.upper() in black_list:
                    print(f'{symbol}在持仓限制名单中，退出程序！')
                    sys.exit()

        # 调整杠杆到3x
        if exchange_1_name == 'binance':
            print(exchange_1.change_swap_leverage(symbol))
        elif exchange_2_name == 'binance':
            print(exchange_2.change_swap_leverage(symbol))

        # 创建任务列表
        tasks = [main()]

        # 只有当交易所是 binance 时才添加监控任务
        if exchange_1_name == 'binance' or exchange_2_name == 'binance':
            tasks.append(monitor_usdt_balance())
            print('启动binance账户余额监控程序...')

        # 添加交易进度监控任务
        tasks.append(monitor_trading_progress_task())
        print('启动交易进度监控程序...')

        await asyncio.gather(*tasks)

    except Exception:
        print(format_exc())
        sendSlackMsgAsync(content=f'{report_title} WebSocket对冲程序:' + '\n' + '程序异常退出！', channel='报错消息')
# endregion ============================================主程序逻辑


# region ============================================设置
symbol = 'eth'
# 卖出合约交易所：
exchange_1_name = 'binance'
# exchange_1_name = 'okx'
# exchange_1_name = 'hyperliquid'
# exchange_1_name = 'gateio'
# exchange_1_name = 'bitget'
# exchange_1_name = 'bybit'
# exchange_1_name = 'backpack'

# 买入合约交易所：
# exchange_2_name = 'binance'
exchange_2_name = 'hyperliquid'
# exchange_2_name = 'bitget'
# exchange_2_name = 'bybit'
# exchange_2_name = 'gateio'
# exchange_2_name = 'okx'
# exchange_2_name = 'backpack'

# 交易参数
r_threshold = 0.0003
usdt_balance = 50000  # USDT余额
close_position = True  # 是否平仓模式

exchange_account = {
    'binance': '<EMAIL>',
    'gateio': '<EMAIL>',
    'bitget': '<EMAIL>',
    'okx': '<EMAIL>',
    'hyperliquid': 'hyperliquid-持仓账号',
    'vertex': 'vertex-持仓资金费',
    'bybit': '<EMAIL>',
    'backpack': '<EMAIL>'
}

exchange_dict = {
    'binance': BinanceAdapter,
    # 'gateio': GateioAdapter,
    'hyperliquid': HyperliquidAdapter,
    # 'bitget': BitgetAdapter,
    # 'vertex': VertexAdapter,
    # 'apexpro': ApexproAdapter,
    'bybit': BybitAdapter,
    'okx': OkxAdapter,
    'backpack': BackpackAdapter
}

# 创建交易所适配器实例
exchange_1 = exchange_dict[exchange_1_name](exchange_account[exchange_1_name])
exchange_2 = exchange_dict[exchange_2_name](exchange_account[exchange_2_name])
exchange_1_name, exchange_2_name = exchange_1.exchange_name, exchange_2.exchange_name

# 获取价格和精度信息
symbol_price = exchange_1.get_swap_buy1(symbol)
max_execute_num = usdt_balance / symbol_price  # 总开仓交易数量
total_amount = 0  # 总交易数量

# 交易控制
trading_enabled = True  # 设置为True启用交易，False仅监控
trade_cooldown = 1.0  # 交易冷却时间(秒)

# 进度显示配置（简化）
progress_interval = 5.0  # 进度更新间隔(秒)

# 获取交易精度
exchange_1_price_precision = exchange_1.get_swap_order_price_tick_size(symbol)
exchange_1_amount_precision = exchange_1.get_swap_order_amount_tick_size(symbol)
exchange_2_price_precision = exchange_2.get_swap_order_price_tick_size(symbol)
exchange_2_amount_precision = exchange_2.get_swap_order_amount_tick_size(symbol)
amount_precision = min(exchange_1_amount_precision, exchange_2_amount_precision)

# 计算最小交易数量
min_execute_amount = round(20 * 1.3 / symbol_price, amount_precision) / 0.8  # 盘口最低币数量要求
min_trade_amount = (min_execute_amount * 1.5 * 10 ** exchange_2_price_precision) / 10 ** exchange_2_price_precision if symbol != 'btc' else 0.002

# 报告标题
report_title = f'{exchange_1_name}/{exchange_2_name}资金费套利程序(WebSocket版-卖买)'

# WebSocket配置
ws_config = WSConfig(
    reconnect_interval=5.0,
    max_reconnect_attempts=10,
    ping_interval=30.0,
    data_timeout=60.0
)

# 订单簿缓存
orderbook_cache = OrderbookCache()

# WebSocket管理器
ws_manager = WebSocketManager(ws_config)

# 回调处理器
callback_handler = None

print(f"配置完成:")
print(f"交易对: {symbol}")
print(f"Exchange 1 (卖出合约): {exchange_1_name}")
print(f"Exchange 2 (买入合约): {exchange_2_name}")
print(f"价差阈值: {r_threshold}")
print(f"最大开仓数量: {max_execute_num}")
print(f"最小交易数量: {min_trade_amount}")
print(f"WebSocket重连间隔: {ws_config.reconnect_interval}秒")
print(f"WebSocket最大重连次数: {ws_config.max_reconnect_attempts}")
print(f"进度更新间隔: {progress_interval}秒")
# endregion ============================================设置


# region ============================================主程序
if __name__ == '__main__':
    try:
        print("启动WebSocket版套利程序(卖买模式)...")
        asyncio.run(run())
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}")
        print(format_exc())
# endregion ============================================主程序
