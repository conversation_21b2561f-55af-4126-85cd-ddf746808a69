# !/usr/bin/env python
# -*- coding:utf-8 -*-
# region ===============================================备注说明
"""
    套利资金费，卖出现货，买入合约 (WebSocket版本)。
    基于WebSocket实时数据流进行套利交易
    Exchange 1: 卖出现货
    Exchange 2: 买入合约 (kcoin)
    使用reverse_arbitrage模式，适用于现货和合约之间的套利
"""
# endregion ============================================备注说明


# region ===============================================import
import asyncio
from math import ceil
from time import sleep
from traceback import format_exc

from kzmfunction.function import sendSlackMsgAsync
from kzmfunction.ExchangeAdapter.exchangeadapter.BinanceAdapter import BinanceAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BybitAdapter import BybitAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.VertexAdapter import VertexAdapter
# from kzmfunction.ExchangeAdapter.exchangeadapter.ApexproAdapter import ApexproAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.GateioAdapter import GateioAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.HyperliquidAdapter import HyperliquidAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BitgetAdapter import BitgetAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.OkxAdapter import OkxAdapter
from kzmfunction.ExchangeAdapter.exchangeadapter.BackpackAdapter import BackpackAdapter
from kzmfunction.WebSocketAdapter.WebSocketCore import (
    # OrderbookData,
    WSConfig,
    OrderbookCache,
    WebSocketManager,
    create_wss_adapter
)
from kzmfunction.WebSocketAdapter.CallbackHandlers import (
    create_callback_handler
)
from kzmfunction.WebSocketAdapter.DisplayUtils import print_red, print_green
# endregion ============================================import


# region ===============================================全局变量
# 交易控制
trading_enabled = True  # 设置为True启用交易，False仅监控
trade_cooldown = 1.0  # 交易冷却时间(秒)

# 进度显示配置
progress_interval = 5.0  # 进度更新间隔(秒)
last_progress_time = 0

# 全局变量
total_amount = 0  # 总交易数量
# endregion ============================================全局变量


# region ===============================================交易逻辑函数
def get_trading_enabled():
    """获取交易启用状态"""
    return trading_enabled


async def trigger_trading_logic_wrapper(exchange_1_data, exchange_2_data):
    """交易逻辑包装器"""
    global trading_enabled
    
    if not trading_enabled:
        return
        
    try:
        await trigger_trading_logic(exchange_1_data, exchange_2_data)
    except Exception as e:
        print(f"交易逻辑执行错误: {e}")
        print(format_exc())
        trading_enabled = False


async def trigger_trading_logic(exchange_1_data, exchange_2_data):
    """
    触发交易逻辑 - 反向套利：卖出现货，买入合约
    :param exchange_1_data: exchange_1 (现货) 订单簿数据
    :param exchange_2_data: exchange_2 (合约) 订单簿数据
    """
    global total_amount, trading_enabled
    
    try:
        # 获取价格数据（数据已在WebSocket接收时转换过）
        # 反向套利：现货买1价格（卖出现货），合约卖1价格（买入合约）
        exchange_1_buy1_price = float(exchange_1_data['bid'][0])  # 现货买1价格（我们卖出的价格）
        exchange_1_buy1_amount = float(exchange_1_data['bid'][1])  # 现货买1数量
        exchange_2_sell1_price = float(exchange_2_data['ask'][0])  # 合约卖1价格（我们买入的价格）
        exchange_2_sell1_amount = float(exchange_2_data['ask'][1])  # 合约卖1数量
        
        # 计算价差 (反向套利：现货买1 / 合约卖1 - 1)
        r = exchange_1_buy1_price / exchange_2_sell1_price - 1
        
        # 检查是否达到最大交易量
        if total_amount >= max_execute_num:
            print('达到最大下单币数，完成建仓计划')
            sendSlackMsgAsync(content=f'{report_title}:\n{symbol}下单完成！', channel='套利消息')
            trading_enabled = False
            return
            
        # 检查剩余交易量是否足够
        if max_execute_num - total_amount < min_trade_amount:
            print(f'交易数量不足{min_trade_amount}个，当前{exchange_2_name}不再交易')
            sendSlackMsgAsync(content=f'{report_title}:\n{symbol}交易数量不足{min_trade_amount}个，当前{exchange_2_name}不再交易！', channel='套利消息')
            trading_enabled = False
            return
        
        # 判断价差是否满足要求
        if r < r_threshold:
            print(f'{exchange_1_name}现货价格:{exchange_1_buy1_price:.7f}，{exchange_2_name}合约价格:{exchange_2_sell1_price:.7f}，价差:{r:.4f}, {exchange_1_name}数量:{exchange_1_buy1_amount:.2f}, {exchange_2_name}数量:{exchange_2_sell1_amount:.2f}')
            print('利差小于目标阀值，不入金')
            return
            
        # 检查盘口数量是否足够
        if exchange_1_buy1_amount <= min_execute_amount or exchange_2_sell1_amount <= min_execute_amount:
            print(f'{exchange_1_name}价格：%.7f，{exchange_2_name}价格：%.7f，价差：%.4f' % (exchange_1_buy1_price, exchange_2_sell1_price, r))
            print(f'{exchange_1_name}盘口数量：', exchange_1_buy1_amount)
            print(f'{exchange_2_name}盘口数量：', exchange_2_sell1_amount)
            print('利差大于目标阀值，但盘口数量不足，不入金')
            return
            
        # 计算交易价格和数量 (反向套利的价格调整)
        exchange_1_price = round(exchange_1_buy1_price * 0.99, exchange_1_price_precision)  # 现货卖出价格
        exchange_2_price = round(exchange_2_sell1_price * 1.01, exchange_2_price_precision)  # 合约买入价格
        execute_amount = round(max(min(exchange_2_sell1_amount, exchange_1_buy1_amount, max_execute_num - total_amount) * 0.8, min_trade_amount), amount_precision)

        # 执行下单
        order_info, order_info2 = await place_order(exchange_1_price, exchange_2_price, execute_amount)
        print(f'{exchange_1_name}现货价格：%.4f, {exchange_2_name}合约价格：%.4f, 价差：%.4f' % (exchange_1_buy1_price, exchange_2_sell1_price, r))
        print('利差和盘口数量大于目标阀值，开始入金')
        print(f'交易计划:{exchange_1_name}卖出现货, 卖出价格{exchange_1_buy1_price}, {exchange_2_name}买入合约, 买入价格{exchange_2_sell1_price}, 交易数量{execute_amount}')

        if not order_info or not order_info2 or not order_info.get('order_id') or 'error' in str(order_info2):
            print('下单失败，请检查订单！！')
            print(order_info)
            print(order_info2)
            sendSlackMsgAsync(content=f'{report_title}:\n{symbol}下单失败，请检查订单！！', channel='套利消息')
            trading_enabled = False
            return
        else:
            # 计算实际下单成交溢价
            sleep(1)
            exchange_1_order_info = exchange_1.get_spot_order_info_kcoin(symbol, order_info.get('order_id'))
            exchange_2_order_info = exchange_2.get_swap_order_info_kcoin(symbol, order_info2.get('order_id'))
            print(exchange_1_order_info)
            print(exchange_2_order_info)
            # 计算实际下单成交价差 (反向套利)
            premium = float(exchange_1_order_info.get('average_price')) / float(exchange_2_order_info.get('average_price')) - 1
            print_green(f'下单预期价差：{r:.4f}，实际成交价差: {premium:.6f}, 溢价：{(premium/r-1)*100:.2f}%')
        
        # 更新累计成交量
        total_amount += execute_amount
        print_red(f'{symbol}进行下一轮循环, 已执行数量{total_amount}, 完成比例: {total_amount / max_execute_num * 100:.2f}%')

    except Exception as e:
        print(f"交易逻辑错误: {e}")
        print(format_exc())


async def place_order(exchange_1_price, exchange_2_price, execute_amount):
    """
    exchange_1/exchange_2下单
    :param exchange_1_price: exchange_1下单价格
    :param exchange_2_price: exchange_2下单价格
    :param execute_amount: 下单数量
    :return:
    """

    # exchange_1下单卖出现货
    async def place_exchange_1_order():
        """
        exchange_1下单卖出现货
        """
        # 下单时需要转换回原始单位（因为API期望的是原始价格和数量）
        if exchange_1_name == 'binance' and exchange_2_name == 'hyperliquid':
            # 将转换后的价格和数量转换回Binance API期望的格式
            original_price = exchange_1_price / 1000  # 转换回原始价格
            original_amount = execute_amount * 1000   # 转换回原始数量
            _order['exchange_1_order'] = exchange_1.place_spot_order(symbol=symbol, direction='sell', order_type='limit', amount=original_amount, price=original_price)
        else:
            _order['exchange_1_order'] = exchange_1.place_spot_order(symbol=symbol, direction='sell', order_type='limit', amount=execute_amount, price=exchange_1_price)

    # exchange_2下单买入合约
    async def place_exchange_2_order():
        """
        exchange_2下单买入合约
        """
        if close_position:
            _order['exchange_2_order'] = exchange_2.place_swap_order_kcoin(symbol=symbol, direction='buy', order_type='limit', amount=execute_amount, price=exchange_2_price, close_position=True)
        else:
            _order['exchange_2_order'] = exchange_2.place_swap_order_kcoin(symbol=symbol, direction='buy', order_type='limit', amount=execute_amount, price=exchange_2_price)

    _order = {}
    
    try:
        # 并发执行下单
        await asyncio.gather(
            place_exchange_1_order(),
            place_exchange_2_order()
        )
        
        exchange_1_order = _order.get('exchange_1_order', None)
        exchange_2_order = _order.get('exchange_2_order', None)
        
        return exchange_1_order, exchange_2_order
        
    except Exception as e:
        print(f"下单异常: {e}")
        print(format_exc())
        return None, None
# endregion ===============================================交易逻辑函数


# region ============================================监控函数
async def monitor_usdt_balance():
    """监控USDT余额"""
    while trading_enabled:
        try:
            await asyncio.sleep(60)  # 每分钟检查一次
            if exchange_1_name == 'binance':
                balance = exchange_1.get_spot_account_single_asset('USDT')
                if balance < 50:
                    print(f"警告: {exchange_1_name} USDT余额不足: {balance}")
                    sendSlackMsgAsync(content=f'{report_title}:\n{exchange_1_name} USDT余额不足: {balance}', channel='报错消息')

            if exchange_2_name == 'binance':
                balance = exchange_2.get_swap_account_single_asset('USDT')
                if balance < 50:
                    print(f"警告: {exchange_2_name} USDT余额不足: {balance}")
                    sendSlackMsgAsync(content=f'{report_title}:\n{exchange_2_name} USDT余额不足: {balance}', channel='报错消息')

        except Exception as e:
            print(f"余额监控错误: {e}")


async def monitor_trading_progress_task():
    """监控交易进度任务"""
    global last_progress_time

    while trading_enabled:
        try:
            await asyncio.sleep(progress_interval)
            current_time = asyncio.get_event_loop().time()

            if current_time - last_progress_time >= progress_interval:
                progress_percentage = (total_amount / max_execute_num * 100) if max_execute_num > 0 else 0
                print_red(f'📊 {symbol} 下单进度: {total_amount:.4f}/{max_execute_num:.4f} ({progress_percentage:.2f}%) | 剩余: {max_execute_num - total_amount:.4f}')
                last_progress_time = current_time

        except Exception as e:
            print(f"进度监控错误: {e}")
# endregion ============================================监控函数


# region ============================================WebSocket设置函数
def create_price_conversion_callback(original_callback, exchange_name):
    """创建价格转换回调包装器"""
    def price_conversion_callback(adapter, data):
        # 如果是Binance现货的最佳买卖价数据，需要进行价格转换
        if (exchange_name == 'binance' and
            data.get('type') == 'best_orderbook' and
            symbol.lower() in ['bonk', 'pepe', 'floki', 'shib']):

            # 创建转换后的数据副本
            converted_data = data.copy()

            # 转换价格和数量：1 kBONK = 1000 BONK
            if 'bid1_price' in converted_data and converted_data['bid1_price']:
                converted_data['bid1_price'] = str(float(converted_data['bid1_price']) * 1000)
            if 'ask1_price' in converted_data and converted_data['ask1_price']:
                converted_data['ask1_price'] = str(float(converted_data['ask1_price']) * 1000)
            if 'bid1_amount' in converted_data and converted_data['bid1_amount']:
                converted_data['bid1_amount'] = str(float(converted_data['bid1_amount']) / 1000)
            if 'ask1_amount' in converted_data and converted_data['ask1_amount']:
                converted_data['ask1_amount'] = str(float(converted_data['ask1_amount']) / 1000)

            # 传递转换后的数据
            return original_callback(adapter, converted_data)
        else:
            # 其他数据直接传递
            return original_callback(adapter, data)

    return price_conversion_callback


async def setup_websockets():
    """设置WebSocket连接"""
    global callback_handler

    try:
        # 初始化回调处理器 (使用reverse_arbitrage模式)
        callback_handler = create_callback_handler(
            'reverse_arbitrage',  # 使用反向套利模式
            orderbook_cache,
            exchange_1_name=exchange_1_name,
            exchange_2_name=exchange_2_name,
            symbol=symbol,
            r_threshold=r_threshold,
            trading_enabled_callback=get_trading_enabled,
            trigger_trading_callback=trigger_trading_logic_wrapper,
            trade_cooldown=trade_cooldown
        )

        # 创建带价格转换的回调函数
        exchange_1_callback = create_price_conversion_callback(
            callback_handler.handle_orderbook_update,
            exchange_1_name
        )
        exchange_2_callback = create_price_conversion_callback(
            callback_handler.handle_orderbook_update,
            exchange_2_name
        )

        # 创建WebSocket适配器
        exchange_1_wss = create_wss_adapter(
            exchange_1_name,
            symbol,
            exchange_1_callback,
            market_type='spot'
        )

        exchange_2_wss = create_wss_adapter(
            exchange_2_name,
            symbol,
            exchange_2_callback,
            market_type='swap'
        )

        if not exchange_1_wss or not exchange_2_wss:
            raise Exception(f"无法创建WebSocket适配器: {exchange_1_name}={exchange_1_wss}, {exchange_2_name}={exchange_2_wss}")

        # 添加到WebSocket管理器
        await ws_manager.add_adapter(f"{exchange_1_name}_spot", exchange_1_wss)
        await ws_manager.add_adapter(f"{exchange_2_name}_swap", exchange_2_wss)

        print(f"WebSocket连接已设置:")
        print(f"  {exchange_1_name} 现货: {symbol}")
        print(f"  {exchange_2_name} 合约: {symbol}")

    except Exception as e:
        print(f"WebSocket设置失败: {e}")
        print(format_exc())
        raise


async def start_websockets():
    """启动WebSocket连接"""
    try:
        await ws_manager.start_all()
        print("所有WebSocket连接已启动")
    except Exception as e:
        print(f"WebSocket启动失败: {e}")
        print(format_exc())
        raise


async def stop_websockets():
    """停止WebSocket连接"""
    try:
        await ws_manager.stop_all()
        print("所有WebSocket连接已停止")
    except Exception as e:
        print(f"WebSocket停止失败: {e}")
        print(format_exc())
# endregion ============================================WebSocket设置函数


# region ============================================主程序逻辑
async def main():
    """主程序逻辑"""
    global trading_enabled

    try:
        print(f"启动 {report_title}")
        print("="*50)

        # 检查持仓限制
        if exchange_2_name == 'hyperliquid' and not close_position:
            black_list = exchange_2.get_swap_contract_at_open_interest_cap()
            if 'k' + symbol.upper() in black_list:
                print(f'{symbol}在持仓限制名单中，退出程序！')
                return
        elif exchange_1_name == 'hyperliquid' and not close_position:
            black_list = exchange_1.get_swap_contract_at_open_interest_cap()
            if 'k' + symbol.upper() in black_list:
                print(f'{symbol}在持仓限制名单中，退出程序！')
                return

        # 调整杠杆
        if exchange_2_name == 'binance':
            print(exchange_2.change_swap_leverage_kcoin(symbol))

        # 检查平仓模式
        if close_position:
            # 查看持仓
            exchange_1_max_execute_num = exchange_1.get_spot_account_single_asset(symbol)
            exchange_2_max_execute_num = float(exchange_2.get_swap_position_kcoin(symbol)[0]['amount'])
            if all([exchange_1_max_execute_num, exchange_2_max_execute_num]):
                global max_execute_num
                max_execute_num = min(exchange_1_max_execute_num, abs(exchange_2_max_execute_num))
            else:
                print('当前账户没有持仓，退出程序！')
                return
            print('当前账户持仓数量：', max_execute_num)
        else:
            print('当前账户最大开仓数量：', max_execute_num)

        # 设置WebSocket
        await setup_websockets()

        # 启动WebSocket
        await start_websockets()

        # 保持程序运行
        while trading_enabled:
            await asyncio.sleep(1)

        print("交易已完成或停止")

    except KeyboardInterrupt:
        print("收到中断信号，正在停止...")
        trading_enabled = False
    except Exception as e:
        print(f"主程序错误: {e}")
        print(format_exc())
        sendSlackMsgAsync(content=f'{report_title}:\n程序异常退出: {e}', channel='报错消息')
    finally:
        await stop_websockets()


async def run_program():
    """运行程序"""
    try:
        # 创建任务列表
        tasks = [main()]

        # 只有当交易所是 binance 时才添加监控任务
        if exchange_1_name == 'binance' or exchange_2_name == 'binance':
            tasks.append(monitor_usdt_balance())
            print('启动binance账户余额监控程序...')

        # 添加交易进度监控任务
        tasks.append(monitor_trading_progress_task())
        print('启动交易进度监控程序...')

        await asyncio.gather(*tasks)

    except Exception:
        print(format_exc())
        sendSlackMsgAsync(content=f'{report_title} WebSocket套利程序:' + '\n' + '程序异常退出！', channel='报错消息')
# endregion ============================================主程序逻辑


# region ============================================设置
symbol = 'bonk'
# 卖出现货交易所：
exchange_1_name = 'binance'
# exchange_1_name = 'gateio'
# exchange_1_name = 'bybit'
# exchange_1_name = 'okx'

# 买入合约交易所：
exchange_2_name = 'hyperliquid'
# exchange_2_name = 'binance'
# exchange_2_name = 'okx'
# exchange_2_name = 'vertex'
# exchange_2_name = 'bitget'

# 交易参数
r_threshold = 0.2
usdt_balance = 100000  # USDT余额
close_position = False  # 是否平仓模式

exchange_account = {
    'binance': '<EMAIL>',
    'gateio': '<EMAIL>',
    'bitget': '<EMAIL>',
    'okx': '<EMAIL>',
    'hyperliquid': 'hyperliquid-持仓账号',
    'vertex': 'vertex-持仓资金费',
    'bybit': '<EMAIL>',
    'backpack': '<EMAIL>'
}

exchange_dict = {
    'binance': BinanceAdapter,
    'gateio': GateioAdapter,
    'hyperliquid': HyperliquidAdapter,
    'bitget': BitgetAdapter,
    # 'vertex': VertexAdapter,
    # 'apexpro': ApexproAdapter,
    'bybit': BybitAdapter,
    'okx': OkxAdapter,
    'backpack': BackpackAdapter
}

# 创建交易所适配器实例
exchange_1 = exchange_dict[exchange_1_name](exchange_account[exchange_1_name])
exchange_2 = exchange_dict[exchange_2_name](exchange_account[exchange_2_name])
exchange_1_name, exchange_2_name = exchange_1.exchange_name, exchange_2.exchange_name

# 获取价格和精度信息
try:
    symbol_price = exchange_1.get_swap_buy1_kcoin(symbol)
    if symbol_price is None:
        # 如果exchange_1获取失败，尝试exchange_2
        symbol_price = exchange_2.get_swap_buy1_kcoin(symbol)
    if symbol_price is None:
        # 如果都失败，使用默认价格
        symbol_price = 0.029  # BONK的大概价格
        print(f"警告：无法获取{symbol}价格，使用默认价格: {symbol_price}")
except Exception as e:
    print(f"获取价格失败: {e}")
    symbol_price = 0.029  # 默认价格

max_execute_num = usdt_balance / symbol_price  # 总开仓交易数量

# 获取交易精度
exchange_1_price_precision = exchange_1.get_spot_order_price_tick_size_kcoin(symbol)
exchange_1_amount_precision = exchange_1.get_spot_order_amount_tick_size_kcoin(symbol)
exchange_2_price_precision = exchange_2.get_swap_order_price_tick_size_kcoin(symbol)
exchange_2_amount_precision = exchange_2.get_swap_order_amount_tick_size_kcoin(symbol)
amount_precision = min(exchange_1_amount_precision, exchange_2_amount_precision)

# 计算最小交易数量
min_execute_amount = round(10 * 1.3 / symbol_price, amount_precision) / 0.8  # 盘口最低币数量要求
min_trade_amount = ceil(min_execute_amount * 1.5 * 10 ** exchange_2_price_precision) / 10 ** exchange_2_price_precision

# 报告标题
report_title = f'{exchange_1_name}/{exchange_2_name}资金费套利程序(WebSocket版-现货合约-反向)'

# WebSocket配置
ws_config = WSConfig(
    reconnect_interval=5.0,
    max_reconnect_attempts=10,
    ping_interval=30.0,
    data_timeout=60.0
)

# 订单簿缓存
orderbook_cache = OrderbookCache()

# WebSocket管理器
ws_manager = WebSocketManager(ws_config)

# 回调处理器
callback_handler = None

print(f"配置完成:")
print(f"交易对: {symbol}")
print(f"Exchange 1 (现货): {exchange_1_name}")
print(f"Exchange 2 (合约): {exchange_2_name}")
print(f"价差阈值: {r_threshold}")
print(f"最大开仓数量: {max_execute_num}")
print(f"最小交易数量: {min_trade_amount}")
print(f"WebSocket重连间隔: {ws_config.reconnect_interval}秒")
print(f"WebSocket最大重连次数: {ws_config.max_reconnect_attempts}")
print(f"进度更新间隔: {progress_interval}秒")
# endregion ============================================设置


# region ============================================程序入口
if __name__ == '__main__':
    try:
        asyncio.run(run_program())
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        print(format_exc())
# endregion ============================================程序入口
