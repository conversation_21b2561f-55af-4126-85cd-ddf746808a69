#!/usr/bin/env python
# -*- coding:utf-8 -*-
from math import ceil
import numpy as np
import pandas as pd
import json
import requests
from time import localtime, strftime, strptime, sleep, mktime
from datetime import datetime, timedelta, timezone
import logging
import threading
import traceback
import os
from requests import get
from json import loads
from urllib.request import urlopen, Request
import sys


# ------------------------------将数据转换为其他周期的函数
def transfer_to_period_data(df, rule_type='15T', save=False, save_name='kline', save_format='h5'):
    """
    将数据转换为其他周期的数据
    :param df:
    :param rule_type: 15t,1h,
    :param save:是否保存转换后的数据
    :param save_format:选择保存的文件格式，默认为csv
    :param save_name:保存的文件名
    :return:
    """
    # =====转换为其他分钟数据
    if 'date' in df.columns:
        period_df = df.resample(rule=rule_type,
                                on='date',
                                label='left',
                                closed='left').agg(dict(open='first', high='max', low='min', close='last', volume='sum'))
    elif 'candle_begin_time' in df.columns:
        period_df = df.resample(rule=rule_type,
                                on='candle_begin_time',
                                label='left',
                                closed='left').agg(dict(open='first', high='max', low='min', close='last', volume='sum'))
    else:
        print('请修改日期列的列名为date或candle_begin_time！')
    period_df.dropna(subset=['open'], inplace=True)  # 去除一天都没有交易的周期
    period_df = period_df[period_df['volume'] > 0]  # 去除成交量为0的交易周期
    period_df.reset_index(inplace=True)
    if 'date' in df.columns:
        df = period_df[['date', 'open', 'high', 'low', 'close', 'volume']]
    elif 'candle_begin_time' in df.columns:
        df = period_df[[
            'candle_begin_time', 'open', 'high', 'low', 'close', 'volume'
        ]]

    if save:
        if save_format == 'h5':
            df.to_hdf(save_name + '.h5', key='all_data')
        elif save_format == 'csv':
            df.to_csv(save_name + '.csv')
        else:
            print('只能保存h5或csv格式的文件，请重新输入并保存！')
    return df


# ------------------------------发送钉钉消息
def sendDingdingMsg(content, robot_id='e8fed732026a3a26f91de95a6825b212b2343bea3f950691045fbbb9202f6958'):
    """
    :param content: 发送的文本内容
    :param robot_id: 机器人id
    :return:
    """
    try:
        msg = {
            "msgtype": "text",
            "text": {
                "content":
                    datetime.now().strftime("%m-%d %H:%M:%S") + '\n' +
                    '------------------------' + '\n' +
                    content
            }
        }
        headers = {"Content-Type": "application/json ;charset=utf-8 "}
        url = 'https://oapi.dingtalk.com/robot/send?access_token=' + robot_id
        body = json.dumps(msg)
        requests.post(url, data=body, headers=headers, timeout=1)
    except Exception as err:
        print(traceback.format_exc())
        print('钉钉发送消息失败', err)


# ------------------------------发送钉钉消息(专用机器人名称:switch开关)
def sendDingdingMsg_robotname_switch(content, robot_id='a97e695dea391b8946614c1714c9207caf8df17e372a0101e84bf2c1b90df068'):
    """
    专门用来发送orderbook-ratio数据的机器人
    :param content: 发送的文本内容
    :param robot_id: 机器人id
    :return:
    """
    try:
        msg = {
            "msgtype": "text",
            "text": {
                "content":
                    '{关键词：我是机器人2}' + '\n' +
                    datetime.now().strftime("%m-%d %H:%M:%S") + '\n' +
                    '--------------------' + '\n' +
                    content
            }
        }
        headers = {"Content-Type": "application/json ;charset=utf-8 "}
        url = 'https://oapi.dingtalk.com/robot/send?access_token=' + robot_id
        body = json.dumps(msg)
        requests.post(url, data=body, headers=headers, timeout=1)
    except Exception as err:
        print(traceback.format_exc())
        print('钉钉发送消息失败', err)


# ------------------------------发送异步钉钉消息
def sendDingdingMsgAsync(content):
    """
    :param content: 发送的文本内容
    :return:
    """
    notify_th = threading.Thread(target=sendDingdingMsg, args=(content,))
    notify_th.start()


# ------------------------------发送slack消息
def sendSlackMsg(content, channel='对冲消息'):
    """
    :param content:
    :param channel:
    :return:
    """
    s_url = '*****************************************************************************'

    dict_headers = {'Content-type': 'application/json'}
    dict_payload = {
        "channel": channel,
        "text": content + '\n' + '---------------------------------------'
    }
    json_payload = json.dumps(dict_payload)

    try:
        rtn = requests.post(s_url, data=json_payload, headers=dict_headers, timeout=1)
        return rtn
    except Exception as err:
        print(traceback.format_exc())
        print('slack发送消息失败', err)

    # print(rtn.text)

    return None


# ------------------------------发送异步slack消息
def sendSlackMsgAsync(content, channel='对冲消息'):
    """
    :param content: 发送的文本内容
    :param channel: 发送的频道   "对冲消息"  "警告消息"  "报错消息" "套利消息"
    :return:
    """
    notify_th = threading.Thread(target=sendSlackMsg, args=(content, channel))
    notify_th.start()


# ------------------------------发送邮件
def sendEmailMsg(subject, content, to_address='<EMAIL>', from_address='<EMAIL>', if_add_time=True):
    """
    :param to_address:收件邮箱地址
    :param subject:主题
    :param content:内容
    :param from_address:发送邮箱地址
    :param if_add_time:
    :return:
    使用foxmail发送邮件的程序
    """
    from email.mime.text import MIMEText
    from smtplib import SMTP
    try:
        if if_add_time:
            msg = MIMEText(datetime.now().strftime("%m-%d %H:%M:%S") +
                           '\n\n' + content)
        else:
            msg = MIMEText(content)
        msg["Subject"] = subject + ' ' + datetime.now().strftime(
            "%m-%d %H:%M:%S")
        msg["From"] = from_address
        msg["To"] = to_address

        username = from_address
        password = 'kzm940125lea'

        # server = SMTP('smtp.qq.com', port=587)
        server = SMTP('smtp.qq.com', port=587)
        server.starttls()
        server.login(username, password)
        server.sendmail(from_address, to_address, msg.as_string())
        server.quit()
        # print('邮件发送成功！')
    except Exception as err:
        print(traceback.format_exc())
        print('邮件发送失败！', err)


# ------------------------------发送异步邮件
def sendEmailMsgAsync(subject, content):
    notify_th = threading.Thread(target=sendEmailMsg,
                                 args=(
                                     subject,
                                     content,
                                 ))
    notify_th.start()


# ------------------------------发送telegram消息
def sendTelegramMsg(content, token='**********************************************', chat_id=465283285):
    """
    :param content: 发送的文本内容
    :param token:
    :param chat_id:
    :return:
    """
    try:
        r = requests.post(f'https://api.telegram.org/bot{token}/sendMessage', json={"chat_id": chat_id, "text": content})
        # msg = {
        #     "msgtype": "text",
        #     "text": {
        #         "content":
        #             datetime.now().strftime("%m-%d %H:%M:%S") + '\n' +
        #             '------------------------' + '\n' +
        #             content
        #     }
        # }
        return r
    except Exception as err:
        print(traceback.format_exc())
        print('Telegram发送消息失败', err)
        return None


# ------------------------------发送imessage消息
def sendImessageMsg(content, address):
    """
    :param content: 发送的文本内容
    :param address: 发送的地址(邮箱，手机)
    :return:
    """
    try:
        from os import system
        # 如果address是手机号，在手机号前+86
        # if address.isdigit():
        #     address = '+86' + address
        # print(address)
        cmd_line = f'imessage --text "{content}" --contacts "{address}"'
        system(cmd_line)
    except Exception as err:
        print(traceback.format_exc())
        print('imessage发送消息失败', err)


# ------------------------------记录控制台信息到日志文件
# todo 有问题，无法记录日志
def loggingRecord(filename='my.log', level=logging.DEBUG):
    """
    :param filename: 保存的日志名
    :param level: 保存的日志内容等级，DEBUG < INFO < WARNING < ERROR < CRITICAL
            只有大于设定等级的日志内容才会输出。其中，DEBUG内容最多，包含最详细的日志信息，CRITICAL最少
    :return:在程序所在的文件夹中产生filename的日志文件
    """
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(filename=filename, level=level, format=log_format)


# ------------------------------毫秒ms转为日期
def msConvertToDatetime(ms):
    """
    将毫秒转为日期的函数
    :return:
    """
    time_array = localtime(ms / 1000)
    other_style_time = strftime("%Y-%m-%d %H:%M:%S", time_array)
    return other_style_time


# ------------------------------日期转为毫秒ms
def datetimeConvertToMs(date_time, type='ms'):
    """
    将日期转为毫秒的函数
    :return:
    """
    if type == 'ms':
        return int(mktime(date_time.timetuple())) * 1000
    elif type == 's':
        return int(mktime(date_time.timetuple()))
    else:
        pass


# ------------------------------将当前时间转换为iso861timestamp
def get_timestamp():
    """
    将当前时间转换为iso861timestamp
    :return:
    """
    now = datetime.now()
    t = now.isoformat("T", "milliseconds")
    return t + "Z"


def TimestampIso8601_to_datetime_UTC8(timestamp_iso8601):
    """
    将iso861timestamp转换为北京时间
    :return:
    """
    if "." not in timestamp_iso8601:  # 当毫秒为0
        utc = datetime.strptime(timestamp_iso8601,
                                '%Y-%m-%dT%H:%M:%SZ')
    else:
        utc = datetime.strptime(timestamp_iso8601,
                                '%Y-%m-%dT%H:%M:%S.%fZ')
    now_time = (utc + timedelta(hours=8)).replace(microsecond=0)
    return now_time


def datetime_to_ISO8601(datetime_type):
    """
    :param datetime_type:
    :return:  将datetime类型时间转换为iso8601格式时间
    """
    iso8601_type = (datetime_type - timedelta(hours=8)).isoformat("T", "milliseconds") + "Z"
    return iso8601_type


def string_to_datetime(string, beijingTime=True):
    """
    :param string:
    :param beijingTime:
    :return:       将字符串转换为datatime类型时间  秒级
    """
    if 'T' in string:
        string = string.replace('T', ' ')
        if '.' in string:
            string = string.split('.')[0]
    else:
        pass
    datetime_type = datetime.strptime(string, '%Y-%m-%d %H:%M:%S')
    if beijingTime:
        return datetime_type + timedelta(hours=8)
    else:
        return datetime_type


def datetime_to_string(_datetime):
    str_time = _datetime.strftime('%Y-%m-%d %H:%M:%S')
    return str_time


def timestamp_to_datetime(timestamp):
    """
    timestamp   ----->  BeijingTime 不保留秒
    :param timestamp:
    :return:
    """
    utc = datetime.fromtimestamp(timestamp / 1000).replace(
        second=0, microsecond=0)  # 将时间戳转换为北京时间
    beijing_time = utc + timedelta(hours=8)
    return beijing_time


def timestamp_to_datetime2(timestamp, beijingTime=False):
    """
    timestamp   ----->  BeijingTime 保留秒
    :param timestamp:
    :param beijingTime:
    :return:
    """
    utc = datetime.fromtimestamp(timestamp / 1000)  # 将时间戳转换为北京时间
    if beijingTime:
        return utc + timedelta(hours=8)
    else:
        return utc


# ms字符串转为datetime
def ms_string_to_datetime(ms, beijingTime=True):
    """
    :param ms:
    :param beijingTime:
    :return:
    """
    if beijingTime:
        now = datetime.fromtimestamp(int(ms) / 1000) - timedelta(hours=8)
    else:
        now = datetime.fromtimestamp(int(ms) / 1000)
    t = now.isoformat("T", "milliseconds")
    return t + "Z"


def string_to_timestamp(string):
    """
    string  ----->  timestamp
    :param string:
    :return:
    """
    timestamp = int(mktime(strptime(string,
                                    "%Y-%m-%d %H:%M:%S"))) * 1000
    return timestamp


# 返回指定日期间的所有日期列表
def get_everyday(begin_date, end_date):
    # 前闭后闭
    date_list = []
    begin_date = datetime.strptime(begin_date, "%Y-%m-%d")
    end_date = datetime.strptime(end_date, "%Y-%m-%d")
    while begin_date <= end_date:
        date_str = begin_date.strftime("%Y-%m-%d")
        date_list.append(date_str)
        begin_date += timedelta(days=1)
    return date_list


# ------------------------------根据ms取凌晨时间点的ms
def get_ms_0time_ms(ms):
    temp = datetime.strptime(msConvertToDatetime(ms), '%Y-%m-%d %H:%M:%S').replace(hour=0, minute=0, second=0, microsecond=0)
    return datetimeConvertToMs(temp)


# ------------------------------返回当前时间的年数，月数，日数，小时数、分钟数
def get_datetime(time_frame='hour'):
    """
    :param time_frame:  'year', 'month', 'day', 'hour', 'minute', 'second'
    :return:
    """
    if time_frame == 'year':
        return datetime.year
    elif time_frame == 'month':
        return datetime.month
    elif time_frame == 'day':
        return datetime.day
    elif time_frame == 'hour':
        return datetime.hour
    elif time_frame == 'minute':
        return datetime.minute
    elif time_frame == 'second':
        return datetime.second
    else:
        return None


# ------------------------------检查服务器时区是否正确
def check_timezone():
    time_zone = str(datetime.now(timezone.utc).astimezone())[-5:]
    if time_zone == '08:00':
        pass
    else:
        sendDingdingMsgAsync(content='当前服务器时区为{}'.format(time_zone))


# ------------------------------对字典dict进行切片
def dict_slice(adict, list_str):
    """
    对字典进行切片操作，例如：dict_slice(d, 4, 8)，dict_slice(d, 5, -1)
    :param adict:
    :param list_str:
    :return:
    """
    keys = adict.keys()
    _dict_slice = {}
    for k in eval('list(keys)' + list_str):
        _dict_slice[k] = adict[k]
    return _dict_slice


# ------------------------------对2个列表求交集
def list_intersection(a, b):
    return list(set(a) & set(b))


# ------------------------------对2个列表求并集
def list_union(a, b):
    return list(set(a).union(set(b)))


# ------------------------------对2个列表求差集(a比b多的部分)
def list_difference(a, b):
    """
    注意，此函数求的是a比b多出来的部分，a，b有顺序之分
    :param a:
    :param b:
    :return:
    例如：list(set([1,2,3,4,5,6]).difference(set([3,4,7])) = [1,2,5,6]
         list(set([3,4,7]).difference(set([1,2,3,4,5,6])) = [7]
    """
    return list(set(a).difference(set(b)))


# ------------------------------对2个列表求差集(a，b均不含对方有的部分)
def list_difference2(a, b):
    """
    注意，此函数求的是a、b相互比对方多出来的部分，a，b无顺序之分
    :param a:
    :param b:
    :return:
    例如：list(set([1,2,3,4,5,6])^set([3,4,7])) = [1,2,5,6,7]
    """
    return list(set(a) ^ set(b))


# ------------------------------round函数
def num_round_arthur(num, n=2):
    """
    @arthur写的四舍五入函数
    python之自带的round函数存在bug
    :param num:
    :param n:
    :return:
    """
    if np.floor(num * (10 ** (n + 1))) % 10 != 5.0:
        value = round(num, n)
    else:
        value = np.floor(num * (10 ** n)) / (10 ** n) + 1 / 10 ** n

    return value


# ------------------------------round函数
def num_truncate_arthur(num, n=2):
    """
    @arthur写的截取浮点数num到n位的函数,用做开仓去掉最小单位时用
    :param num:
    :param n:
    :return:
    """
    if np.floor(num * (10 ** (n + 1))) % 10 != 5.0:
        value = round(num, n)
    else:
        value = np.floor(num * (10 ** n)) / (10 ** n) + 1 / 10 ** n

    return value


# ------------------------------round函数(无四舍五入)
def split_float_number(num, pos=3):
    """
    round函数(无四舍五入)
    @param num:
    @param pos:
    @return:
    """
    if '-' in str(abs(num)):
        print('输入的数据太小，无法进行round处理,返回0')
        return 0
    else:
        temp_str_num = str(num).split('.')
        rs = temp_str_num[0] + '.' + temp_str_num[1][:min(pos, len(temp_str_num[1]))]
        return float(rs)


# ------------------------------截取字符串中的数字
def get_number(_str):
    """
    截取字符串中的数字
    @param _str:
    @return:
    """
    return float(''.join([x for x in _str if x.isdigit()]))


# ------------------------------读取url中的数据信息
def get_data_from_url(url_tt, headers=''):
    """
    :param url_tt:
    :param headers:
    :return:
    """
    # HTTP POST Request
    if not headers:
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.100 Safari/537.36',
                   'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                   'accept-encoding': 'gzip, deflate, br', 'Content-Type': 'application/json', }
    while True:
        retry_num = 0
        try:
            r = get(url_tt, headers=headers, timeout=3).text
            r_dict = loads(r)
            if r_dict:
                return r_dict
            else:
                raise Exception
        except Exception as e:
            print(e)
            retry_num += 1
            if retry_num > 5:
                sendSlackMsgAsync(content='下载url数据出错,请检查程序!', channel='报错消息')
                break


# ------------------------------读取url中的数据信息
def get_data_from_url2(url_tt, headers=''):
    """
    :param url_tt:
    :param headers:
    :return:
    """
    if not headers:
        headers = {'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                   'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2', 'Content-Type': 'application/json', }
    else:
        headers = headers
    try:
        req = Request(url=url_tt, headers=headers)
    except Exception as e:
        print(e)
        return None
    else:
        return loads(urlopen(req, timeout=10).read())


# ------------------------------下次运行时间，和课程里面讲的函数是一样的
def next_run_time(time_interval, ahead_seconds=5):
    """
    根据time_interval，计算下次运行的时间，下一个整点时刻。
    目前只支持分钟和小时。
    :param time_interval: 运行的周期，15m，1h
    :param ahead_seconds: 预留的目标时间和当前时间的间隙
    :return: 下次运行的时间
    案例：
    15m  当前时间为：12:50:51  返回时间为：13:00:00
    15m  当前时间为：12:39:51  返回时间为：12:45:00
    10m  当前时间为：12:38:51  返回时间为：12:40:00
    5m  当前时间为：12:33:51  返回时间为：12:35:00
    5m  当前时间为：12:34:51  返回时间为：12:35:00

    1h  当前时间为：14:37:51  返回时间为：15:00:00
    2h  当前时间为：00:37:51  返回时间为：02:00:00

    30m  当前时间为：21日的23:33:51  返回时间为：22日的00:00:00
    5m  当前时间为：21日的23:57:51  返回时间为：22日的00:00:00

    ahead_seconds = 5
    15m  当前时间为：12:59:57  返回时间为：13:15:00，而不是 13:00:00
    """
    if time_interval.endswith('m') or time_interval.endswith('h'):
        pass
    elif time_interval.endswith('T'):
        time_interval = time_interval.replace('T', 'm')
    elif time_interval.endswith('H'):
        time_interval = time_interval.replace('H', 'h')
    else:
        print('time_interval格式不符合规范。程序exit')
        exit()

    ti = pd.to_timedelta(time_interval)
    now_time = datetime.now()
    # now_time = datetime(2019, 5, 9, 23, 50, 30)  # 指定now_time，可用于测试
    this_midnight = now_time.replace(hour=0, minute=0, second=0, microsecond=0)
    min_step = timedelta(minutes=1)

    target_time = now_time.replace(second=0, microsecond=0)

    while True:
        target_time = target_time + min_step
        delta = target_time - this_midnight
        if delta.seconds % ti.seconds == 0 and (target_time - now_time).seconds >= ahead_seconds:
            # 当符合运行周期，并且目标时间有足够大的余地，默认为60s
            break

    print('\n程序下次运行的时间：', target_time, '\n')
    return target_time


# ------------------------------依据时间间隔, 自动计算并休眠到指定时间
def sleep_until_run_time(time_interval, ahead_time=1, if_sleep=True):
    """
    根据next_run_time()函数计算出下次程序运行的时候，然后sleep至该时间
    :param time_interval:
    :param ahead_time:
    :param if_sleep:
    :return:
    """

    # 计算下次运行时间
    run_time = next_run_time(time_interval, ahead_time)

    # sleep
    if if_sleep:
        sleep(max(0, (run_time - datetime.now()).seconds))
        # 可以考察：print(run_time - n)、print((run_time - n).seconds)
        while True:  # 在靠近目标时间时
            if datetime.now() > run_time:
                break

    return run_time


# ------------------------------重试机制
def retry_wrapper(func, params={}, act_name='', sleep_seconds=3, retry_times=5):
    """
    需要在出错时不断重试的函数，例如和交易所交互，可以使用本函数调用。
    :param func: 需要重试的函数名
    :param params: func的参数
    :param act_name: 本次动作的名称
    :param sleep_seconds: 报错后的sleep时间
    :param retry_times: 为最大的出错重试次数
    :return:
    """

    for _ in range(retry_times):
        try:
            result = func(params=params)
            return result
        except Exception as e:
            print(act_name, '报错，报错内容：', str(e), '程序暂停(秒)：', sleep_seconds)
            sleep(sleep_seconds)
    else:
        # send_dingding_and_raise_error(output_info)
        raise ValueError(act_name, '报错重试次数超过上限，程序退出。')


# ------------------------------获取rss内容
def get_rss(_rss_url):
    """
    :param _rss_url:
    :return:
    """
    from feedparser import parse
    rss_content = parse(_rss_url)
    rss_list = [{'title': entry['title'], 'link': entry['link']} for entry in rss_content['entries']]
    return rss_list


# ------------------------------播放自定义语音
def play_voice(txt):
    from pyttsx3 import init
    a = init()
    a.say(txt)
    a.runAndWait()


# ------------------------------播放指定的mp3文件语音
def play_sound(file='text', play_time=1):
    from playsound import playsound
    for i in range(play_time):
        playsound(f'/Volumes/Cryptomator/quant/pycharmproject/kzmfunction/sound/{file}.mp3')


# ------------------------------用宝信发送短信
def sendSms(phone, content):
    """
    发送短信
    :param phone:
    :param content:
    :return:
    """
    from urllib.parse import urlencode

    def md5(str):
        """
        md5加密
        :param str:
        :return:
        """
        from hashlib import md5
        m = md5()
        m.update(str.encode("utf8"))
        return m.hexdigest()

    statusStr = {
        '0': '短信发送成功',
        '-1': '参数不全',
        '-2': '服务器空间不支持,请确认支持curl或者fsocket,联系您的空间商解决或者更换空间',
        '30': '密码错误',
        '40': '账号不存在',
        '41': '余额不足',
        '42': '账户已过期',
        '43': 'IP地址限制',
        '50': '内容含有敏感词'
    }

    smsapi = "http://api.smsbao.com/"
    user = 'kewell2000'
    password = md5("934cd1920e8141dc9c7ffab4832418d5")
    content = content
    phone = phone

    data = urlencode({'u': user, 'p': password, 'm': phone, 'c': content})
    send_url = smsapi + 'sms?' + data
    response = urlopen(send_url)
    the_page = response.read().decode('utf-8')
    print(statusStr[the_page])


# TODO：------------------------------获取邮件内容
def get_email_content(username, password, imap_server):
    import imaplib
    import email
    from email.header import decode_header

    # 连接到 IMAP 服务器
    mail = imaplib.IMAP4_SSL(imap_server, 993)
    mail.login(username, password)
    status = mail.select("INBOX")
    print(status)
    # 搜索所有邮件
    status, messages = mail.search(None, 'ALL')
    print(status)
    print(messages)
    exit()
    # 获取最新一封邮件
    messages = messages[0].split(b' ')
    latest_email_id = messages[-1]

    # 获取邮件内容
    status, msg_data = mail.fetch(latest_email_id, '(RFC822)')
    raw_email = msg_data[0][1]

    # 解析邮件内容
    email_message = email.message_from_bytes(raw_email)

    # 关闭连接
    mail.close()
    mail.logout()

    # 获取邮件主题和发件人
    subject = decode_header(email_message["subject"])[0][0]
    if isinstance(subject, bytes):
        subject = subject.decode()
    from_ = email_message.get("From")

    print("Subject:", subject)
    print("From:", from_)

    # 获取邮件正文
    if email_message.is_multipart():
        for part in email_message.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))
            if "attachment" not in content_disposition:
                body = part.get_payload(decode=True).decode()
                print("Body:", body)
    else:
        body = email_message.get_payload(decode=True).decode()
        print("Body:", body)


# username = "<EMAIL>"
# password = '538YAB"Lj7Z}&jE{wr?^''u]'
# imap_server = "outlook.office365.com"
# get_email_content(username, password, imap_server)

# ------------------------------重启程序
def restart_program():
    python = sys.executable
    os.execl(python, python, *sys.argv)


# 根据指定位数向上取数
def round_to_precision(number, precision):
    return ceil(number * 10 ** precision) / 10 ** precision


# 获取打印颜色的ANSI代码
def get_print_color(color):
    color_dict = {
        'red': "\033[31m",
        'green': "\033[32m",
        'yellow': "\033[33m",
        'blue': "\033[34m",
        'magenta': "\033[35m",      # 品红色
        'cyan': "\033[36m",        # 青色
    }
    return color_dict[color]
