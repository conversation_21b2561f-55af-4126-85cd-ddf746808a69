# kzmfunction/WebSocketAdapter/HyperliquidWssAdapter.py

import asyncio
import json
import websockets
from ..WssAdapter import WssAdapter

class HyperliquidWssAdapter(WssAdapter):
    """
    Hyperliquid交易所的WebSocket适配器。
    支持永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URI = "wss://api.hyperliquid.xyz/ws"
    _EXCHANGE_NAME = "hyperliquid"
    _DEFAULT_QUOTE_CURRENCY = 'usdc'

    # 标准化流名称到Hyperliquid订阅类型的映射
    _STREAM_NAME_MAP = {
        'best_orderbook': 'bbo',
        'orderbook': 'l2Book',
        'trades': 'trades',
        'allMids': 'allMids',
        # kline@... 格式特殊处理, 无需在此映射
    }

    def __init__(self, symbol: str, market_type: str, streams: list, on_message_callback, quote_currency: str = None):
        """
        初始化Hyperliquid WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'BTC'.
            market_type (str): 市场类型, 目前只支持 'swap' (永续合约).
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'trades', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdc'.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in ['swap', 'perp']:
            raise ValueError(f"Unsupported market_type: {market_type}. Hyperliquid only supports 'swap' or 'perp'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")
        
        self.streams = streams
        self._ws = None
        self._subscriptions = []  # 存储订阅信息
        
        # 数据转换器映射 (key是Hyperliquid返回的频道类型)
        self._data_transformers = {
            'l2Book': self._transform_depth_update,
            'trades': self._transform_trade,
            'candle': self._transform_kline,
            'bbo': self._transform_book_ticker,
            'allMids': self._transform_all_mids,
        }
    
    # 构建交易对
    def _construct_symbol(self) -> str:
        """
        构建Hyperliquid的完整交易对，例如 'BTC' 或 'kBONK'。
        Hyperliquid使用币种名称，不需要计价货币后缀。
        如果symbol已经是正确格式（如kBONK），则保持不变。
        """
        # 如果symbol已经以'k'开头，说明已经是正确的kcoin格式，保持不变
        if self.base_symbol.startswith('k') and len(self.base_symbol) > 1:
            return self.base_symbol
        else:
            return self.base_symbol.upper()

    def get_kline_stream_name(self, interval: str = '1m') -> str:
        return f"candle@{interval}"

    def get_trade_stream_name(self) -> str:
        return 'trades'

    def get_bbo_stream_name(self) -> str:
        return 'bbo'

    def get_orderbook_stream_name(self) -> str:
        return 'l2Book'

    def _get_hyperliquid_interval(self, interval: str) -> str:
        """将标准间隔转换为Hyperliquid支持的间隔"""
        interval_map = {
            '1m': '1m',
            '3m': '3m',
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '2h': '2h',
            '4h': '4h',
            '8h': '8h',
            '12h': '12h',
            '1d': '1d',
            '3d': '3d',
            '1w': '1w',
            '1M': '1M'
        }
        return interval_map.get(interval, '1m')

    def _build_subscriptions(self) -> list:
        """构建Hyperliquid的订阅消息列表"""
        subscriptions = []
        
        for stream in self.streams:
            if stream.startswith('kline@'):
                # 处理K线订阅，例如 'kline@1m'
                interval = stream.split('@')[1]
                hyperliquid_interval = self._get_hyperliquid_interval(interval)
                subscription = {
                    "type": "candle",
                    "coin": self._symbol,
                    "interval": hyperliquid_interval
                }
                subscriptions.append(subscription)
            else:
                # 处理其他类型的订阅
                hyperliquid_type = self._STREAM_NAME_MAP.get(stream)
                if hyperliquid_type:
                    if hyperliquid_type == 'allMids':
                        # allMids 不需要coin参数
                        subscription = {"type": hyperliquid_type}
                    else:
                        subscription = {
                            "type": hyperliquid_type,
                            "coin": self._symbol
                        }
                    subscriptions.append(subscription)
                else:
                    print(f"Warning: Unsupported stream name '{stream}' will be ignored.")

        return subscriptions

    # --- 数据转换函数 ---
    def _transform_depth_update(self, data: dict) -> dict:
        """转换Hyperliquid订单簿数据为标准格式"""
        return {
            'type': 'orderbook',
            # 'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('time', 0) * 1000,  # 转换为毫秒
            'bids': [[level['px'], level['sz']] for level in data.get('levels', [[], []])[0]],
            'asks': [[level['px'], level['sz']] for level in data.get('levels', [[], []])[1]],
            # 'raw_data': data
        }

    def _transform_trade(self, data: list) -> dict:
        """转换Hyperliquid交易数据为标准格式"""
        if not data:
            return {}
        
        # 取最新的交易数据
        trade = data[-1] if isinstance(data, list) else data
        return {
            'type': 'trade',
            # 'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': trade.get('time', 0) * 1000,  # 转换为毫秒
            'price': trade.get('px'),
            'quantity': trade.get('sz'),
            'side': trade.get('side'),
            'trade_id': trade.get('tid'),
        }

    def _transform_kline(self, data: list) -> dict:
        """转换Hyperliquid K线数据为标准格式"""
        if not data:
            return {}
        
        # 取最新的K线数据
        candle = data[-1] if isinstance(data, list) else data
        return {
            'type': 'kline',
            # 'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': candle.get('t', 0),  # 开盘时间已经是毫秒
            'interval': candle.get('i'),
            'open': str(candle.get('o', 0)),
            'high': str(candle.get('h', 0)),
            'low': str(candle.get('l', 0)),
            'close': str(candle.get('c', 0)),
            'volume': str(candle.get('v', 0)),
            'trade_count': candle.get('n', 0),
            # 'raw_data': data
        }
    
    def _transform_book_ticker(self, data: dict) -> dict:
        """转换Hyperliquid最佳买卖价数据为标准格式"""
        bbo = data.get('bbo', [None, None])
        return {
            'type': 'best_orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('time', 0) * 1000,  # 转换为毫秒
            'bid1_price': bbo[0]['px'] if bbo[0] else None,
            'bid1_amount': bbo[0]['sz'] if bbo[0] else None,
            'ask1_price': bbo[1]['px'] if bbo[1] else None,
            'ask1_amount': bbo[1]['sz'] if bbo[1] else None,
        }

    def _transform_all_mids(self, data: dict) -> dict:
        """转换Hyperliquid所有中间价数据为标准格式"""
        return {
            'type': 'all_mids',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(asyncio.get_event_loop().time() * 1000),
            'mids': data.get('mids', {}),
        }

    def _transform_unknown(self, data: dict) -> dict:
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'raw_data': data
        }

    async def _connect(self):
        """连接到Hyperliquid的WebSocket服务器"""
        print(f"Connecting to {self._BASE_URI}...")
        self._ws = await websockets.connect(self._BASE_URI)
        print(f"Connected to {self._BASE_URI}.")

    async def _subscribe(self):
        """发送订阅消息"""
        subscriptions = self._build_subscriptions()
        self._subscriptions = subscriptions
        
        for subscription in subscriptions:
            subscribe_message = {
                "method": "subscribe",
                "subscription": subscription
            }
            await self._ws.send(json.dumps(subscribe_message))
            print(f"Subscription message sent: {subscribe_message}")

    async def _process_messages(self):
        """处理从Hyperliquid WebSocket接收到的消息"""
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        async for message in self._ws:
            try:
                payload = json.loads(message)
                
                # 检查是否是订阅响应
                if payload.get('method') == 'subscribe' and 'subscription' in payload:
                    print(f"Hyperliquid subscription confirmed for {payload.get('subscription', {}).get('coin', 'unknown')}")
                    continue
                
                # 处理数据消息
                channel = payload.get('channel')
                data = payload.get('data')

                if channel and data is not None:
                    # 根据频道类型查找并使用对应的转换器
                    transformer = self._data_transformers.get(channel)
                    if transformer:
                        transformed_data = transformer(data)

                        # 将转换后的数据传递给回调函数
                        if self._on_message_callback and transformed_data:
                            self._on_message_callback(self, transformed_data)
                    else:
                        # 处理未知类型的消息
                        print(f"Debug: Hyperliquid unknown channel '{channel}' with data: {data}")
                        print(f"Debug: Available transformers: {list(self._data_transformers.keys())}")
                        transformed_data = self._transform_unknown(payload)
                        if self._on_message_callback:
                            self._on_message_callback(self, transformed_data)

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")

    async def start(self):
        """启动WebSocket连接和消息处理"""
        await self._connect()
        await self._subscribe()
        await self._process_messages()

    async def stop(self):
        """停止WebSocket连接"""
        await super().stop()
        if self._ws:
            await self._ws.close()
            self._ws = None
        print(f"HyperliquidWssAdapter for {self._symbol} connection closed.")
