# kzmfunction/WebSocketAdapter/BitgetWssAdapter.py

import asyncio
import json
import websockets
import time
import hmac
import base64
# import hashlib
from ..WssAdapter import WssAdapter

class BitgetWssAdapter(WssAdapter):
    """
    Bitget交易所的WebSocket适配器。
    支持现货和永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URIS = {
        'spot': "wss://ws.bitget.com/spot/v1/stream",
        'swap': "wss://ws.bitget.com/mix/v1/stream"
    }
    _EXCHANGE_NAME = "bitget"
    _DEFAULT_QUOTE_CURRENCY = 'usdt'

    # 标准化流名称到Bitget流名称的映射
    _STREAM_NAME_MAP = {
        'best_orderbook': 'ticker',
        'orderbook': 'books',
        'trades': 'trade',
        'aggtrade': 'trade',  # Bitget没有聚合交易，使用trade替代
        # kline@... 格式特殊处理, 无需在此映射
    }

    # Bitget的K线间隔映射
    _KLINE_INTERVAL_MAP = {
        '1m': '1m',
        '3m': '3m',
        '5m': '5m',
        '15m': '15m',
        '30m': '30m',
        '1h': '1h',
        '2h': '2h',
        '4h': '4h',
        '6h': '6h',
        '12h': '12h',
        '1d': '1d',
        '1w': '1w'
    }

    def __init__(self, symbol: str, market_type: str, streams: list, on_message_callback, quote_currency: str = None, 
                 api_key: str = None, api_secret: str = None, passphrase: str = None):
        """
        初始化Bitget WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'btc'.
            market_type (str): 市场类型, 'spot' or 'swap'.
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'trades', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdt'.
            api_key (str, optional): API Key for authentication.
            api_secret (str, optional): API Secret for authentication.
            passphrase (str, optional): API Passphrase for authentication.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in self._BASE_URIS:
            raise ValueError(f"Unsupported market_type: {market_type}. Must be 'spot' or 'swap'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")
        
        self.streams = streams
        self._ws = None
        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = passphrase
        
        # 数据转换器映射 (key是Bitget返回的事件类型)
        self._data_transformers = {
            'ticker': self._transform_book_ticker,
            'books': self._transform_depth_update,
            'trade': self._transform_trade,
            'candle': self._transform_kline,
        }
    
    def _construct_symbol(self) -> str:
        """
        构建Bitget的完整交易对，例如 'BTCUSDT'。
        """
        quote = self.quote_currency if self.quote_currency else self._DEFAULT_QUOTE_CURRENCY
        # Bitget 的现货和合约都使用相同的交易对格式，不需要 _UMCBL 后缀
        return f"{self.base_symbol.upper()}{quote.upper()}"

    def _get_inst_type(self) -> str:
        """获取Bitget的产品类型"""
        if self.market_type == 'spot':
            return 'sp'
        else:  # swap
            return 'mc'  # 'mc' for USDT合约

    def _get_bitget_interval(self, interval: str) -> str:
        """将标准间隔转换为Bitget间隔格式"""
        return self._KLINE_INTERVAL_MAP.get(interval, '1m')

    def _build_subscriptions(self) -> list:
        """构建Bitget的订阅消息列表"""
        subscriptions = []
        inst_type = self._get_inst_type()
        
        for stream in self.streams:
            if stream.startswith('kline@'):
                # 处理K线订阅，例如 'kline@1m'
                interval = stream.split('@')[1]
                bitget_interval = self._get_bitget_interval(interval)
                channel = f"candle{bitget_interval}"
                subscriptions.append({
                    "instType": inst_type,
                    "channel": channel,
                    "instId": self._symbol
                })
            else:
                # 处理其他类型的订阅
                bitget_stream = self._STREAM_NAME_MAP.get(stream)
                if bitget_stream:
                    subscriptions.append({
                        "instType": inst_type,
                        "channel": bitget_stream,
                        "instId": self._symbol
                    })
                else:
                    print(f"Warning: Unsupported stream name '{stream}' will be ignored.")

        return subscriptions

    def _generate_signature(self, timestamp: str) -> str:
        """生成Bitget API签名"""
        if not self.api_key or not self.api_secret or not self.passphrase:
            return None
            
        message = timestamp + 'GET' + '/user/verify'
        mac = hmac.new(
            bytes(self.api_secret, encoding='utf8'),
            bytes(message, encoding='utf-8'),
            digestmod='sha256'
        )
        d = mac.digest()
        return base64.b64encode(d).decode()

    # --- 数据转换函数 ---
    def _transform_depth_update(self, data: dict) -> dict:
        """转换Bitget订单簿数据为标准格式"""
        try:
            if not isinstance(data, dict) or 'data' not in data:
                return {}
                
            # Bitget的数据可能是列表或字典
            data_content = data.get('data', [])
            if not data_content:
                return {}
                
            # 如果data是列表，取第一个元素
            if isinstance(data_content, list):
                if not data_content:
                    return {}
                item = data_content[0]
            else:
                item = data_content
                
            # 确保asks和bids存在
            asks = item.get('asks', []) if isinstance(item, dict) else []
            bids = item.get('bids', []) if isinstance(item, dict) else []
            
            # 获取时间戳，如果不存在则使用当前时间
            ts = item.get('ts', int(time.time() * 1000)) if isinstance(item, dict) else int(time.time() * 1000)
            
            return {
                'type': 'orderbook',
                'symbol': self._symbol,
                'market_type': self.market_type,
                'timestamp_ms': int(ts),
                'bids': bids[:5],  # 取前5档
                'asks': asks[:5],  # 取前5档
            }
        except Exception as e:
            print(f"Error in _transform_depth_update: {e}")
            return {}

    def _transform_trade(self, data: dict) -> dict:
        """转换Bitget交易数据为标准格式"""
        try:
            if not isinstance(data, dict) or 'data' not in data:
                return {}
                
            # Bitget的数据可能是列表或字典
            data_content = data.get('data', [])
            if not data_content:
                return {}
                
            # 如果data是列表，取第一个元素
            if isinstance(data_content, list):
                if not data_content:
                    return {}
                item = data_content[0]
            else:
                item = data_content
                
            # 确保必要字段存在
            if not isinstance(item, dict):
                return {}
                
            return {
                'type': 'trades',
                'symbol': self._symbol,
                'market_type': self.market_type,
                'timestamp_ms': int(item.get('ts', int(time.time() * 1000))),
                'price': item.get('price', '0'),
                'quantity': item.get('size', '0'),
                'is_buyer_maker': item.get('side') == 'sell',  # 'buy' 是taker买入, 'sell' 是taker卖出
            }
        except Exception as e:
            print(f"Error in _transform_trade: {e}")
            return {}

    def _transform_kline(self, data: dict) -> dict:
        """转换Bitget K线数据为标准格式"""
        try:
            if not isinstance(data, dict) or 'data' not in data:
                return {}
                
            # Bitget的数据可能是列表或字典
            data_content = data.get('data', [])
            if not data_content:
                return {}
                
            # 如果data是列表，取第一个元素
            if isinstance(data_content, list):
                if not data_content:
                    return {}
                item = data_content[0]
            else:
                item = data_content
                
            # 确保必要字段存在
            if not isinstance(item, dict):
                return {}
                
            return {
                'type': 'kline',
                'symbol': self._symbol,
                'market_type': self.market_type,
                'timestamp_ms': int(item.get('ts', int(time.time() * 1000))),
                'interval': item.get('granularity', '1m'),
                'open': item.get('open', '0'),
                'high': item.get('high', '0'),
                'low': item.get('low', '0'),
                'close': item.get('close', '0'),
                'volume': item.get('volume', '0'),
            }
        except Exception as e:
            print(f"Error in _transform_kline: {e}")
            return {}

    def _transform_book_ticker(self, data: dict) -> dict:
        """转换Bitget最佳买卖价数据为标准格式"""
        try:
            if not isinstance(data, dict) or 'data' not in data:
                return {}
                
            # Bitget的数据可能是列表或字典
            data_content = data.get('data', [])
            if not data_content:
                return {}
                
            # 如果data是列表，取第一个元素
            if isinstance(data_content, list):
                if not data_content:
                    return {}
                item = data_content[0]
            else:
                item = data_content
                
            # 确保必要字段存在
            if not isinstance(item, dict):
                return {}
                
            # 检查必需字段是否存在 (Bitget 使用 bestBid/bestAsk)
            required_fields = ['bestBid', 'bestAsk', 'bidSz', 'askSz']
            missing_fields = [field for field in required_fields if item.get(field) is None]

            if missing_fields:
                # 如果缺少关键字段，返回空字典表示跳过此消息
                return {}

            # 获取时间戳，优先使用 ts，然后是 systemTime
            timestamp = item.get('ts') or item.get('systemTime') or int(time.time() * 1000)

            return {
                'type': 'best_orderbook',
                'symbol': self._symbol,
                'market_type': self.market_type,
                'timestamp_ms': int(timestamp),
                'bid1_price': item.get('bestBid', '0'),  # Bitget 使用 bestBid
                'bid1_amount': item.get('bidSz', '0'),
                'ask1_price': item.get('bestAsk', '0'),  # Bitget 使用 bestAsk
                'ask1_amount': item.get('askSz', '0'),
            }
        except Exception as e:
            print(f"Error in _transform_book_ticker: {e}")
            return {}

    def _transform_unknown(self, data: dict) -> dict:
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
        }

    async def _connect(self):
        """连接到Bitget的WebSocket服务器"""
        stream_url = self._BASE_URIS[self.market_type]
        print(f"Connecting to {stream_url}...")
        self._ws = await websockets.connect(stream_url)
        print(f"Connected to {stream_url}.")

    async def _login(self):
        """登录Bitget WebSocket API (如果提供了API凭证)"""
        if not self.api_key or not self.api_secret or not self.passphrase:
            print("No API credentials provided, skipping login.")
            return
            
        timestamp = str(int(time.time()))
        sign = self._generate_signature(timestamp)
        
        login_message = {
            "op": "login",
            "args": [{
                "apiKey": self.api_key,
                "passphrase": self.passphrase,
                "timestamp": timestamp,
                "sign": sign
            }]
        }
        
        await self._ws.send(json.dumps(login_message))
        response = await self._ws.recv()
        print(f"Login response: {response}")

    async def _subscribe(self):
        """发送订阅消息"""
        subscriptions = self._build_subscriptions()
        
        subscribe_message = {
            "op": "subscribe",
            "args": subscriptions
        }
        
        await self._ws.send(json.dumps(subscribe_message))
        print(f"Subscription message sent: {subscribe_message}")

    async def _process_messages(self):
        """处理从Bitget WebSocket接收到的消息"""
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        # 如果有API凭证，先登录
        if self.api_key and self.api_secret and self.passphrase:
            await self._login()
            
        # 发送订阅消息
        await self._subscribe()

        # 处理接收到的消息
        async for message in self._ws:
            try:
                if message == 'pong':
                    continue
                    
                payload = json.loads(message)
                
                # 处理心跳消息
                if isinstance(payload, dict) and 'event' in payload and payload['event'] == 'ping':
                    pong_message = {"op": "pong"}
                    await self._ws.send(json.dumps(pong_message))
                    continue
                
                # 处理订阅响应
                if isinstance(payload, dict) and 'event' in payload and payload['event'] == 'subscribe':
                    print(f"Subscription confirmed: {payload}")
                    continue
                
                # 处理数据消息
                if isinstance(payload, dict) and 'arg' in payload and 'data' in payload:
                    channel = payload['arg'].get('channel')
                    
                    # 根据频道类型查找并使用对应的转换器
                    transformer = None
                    for key in self._data_transformers:
                        if channel and key in channel:
                            transformer = self._data_transformers[key]
                            break
                    
                    if transformer:
                        transformed_data = transformer(payload)
                        
                        # 将转换后的数据传递给回调函数
                        if self._on_message_callback and transformed_data:
                            self._on_message_callback(self, transformed_data)
                    else:
                        # 处理未知类型的消息
                        transformed_data = self._transform_unknown(payload)
                        if self._on_message_callback:
                            self._on_message_callback(self, transformed_data)

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")
                print(f"Message that caused error: {message[:200]}...")  # 打印导致错误的消息前200个字符

    async def _run(self):
        """运行WebSocket连接和消息处理"""
        try:
            await self._connect()
            await self._process_messages()
        except websockets.exceptions.ConnectionClosed:
            print("Connection closed, attempting to reconnect...")
            await asyncio.sleep(1)
            await self._run()
        except Exception as e:
            print(f"Error in WebSocket connection: {e}")
            await asyncio.sleep(1)
            await self._run()

    async def _keep_alive(self):
        """保持WebSocket连接活跃"""
        while self._ws and self._ws.open:
            try:
                ping_message = {"op": "ping"}
                await self._ws.send(json.dumps(ping_message))
                await asyncio.sleep(25)  # Bitget建议25秒发送一次心跳
            except Exception as e:
                print(f"Error sending ping: {e}")
                break

    async def start(self):
        """启动WebSocket连接"""
        self._task = asyncio.create_task(self._run())
        self._keep_alive_task = asyncio.create_task(self._keep_alive())

    async def stop(self):
        """停止WebSocket连接"""
        if hasattr(self, '_keep_alive_task') and self._keep_alive_task:
            self._keep_alive_task.cancel()
            try:
                await self._keep_alive_task
            except asyncio.CancelledError:
                pass
            self._keep_alive_task = None
            
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
            self._task = None
        
        if self._ws:
            await self._ws.close()
            self._ws = None