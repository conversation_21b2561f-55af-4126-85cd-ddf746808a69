# kzmfunction/WebSocketAdapter/BackpackWssAdapter.py
"""
Backpack交易所的WebSocket适配器模块。
"""

import json
import websockets
from ..WssAdapter import WssAdapter

class BackpackWssAdapter(WssAdapter):
    """
    Backpack交易所的WebSocket适配器。
    支持现货和永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URI = "wss://ws.backpack.exchange"
    _EXCHANGE_NAME = "backpack"
    _DEFAULT_QUOTE_CURRENCY = 'usdc'

    # 标准化流名称到Backpack流名称的映射
    _STREAM_NAME_MAP = {
        'best_orderbook': 'bookTicker',
        'orderbook': 'depth',
        'trades': 'trade',
        'ticker': 'ticker',
        'mark_price': 'markPrice',
        'liquidation': 'liquidation',
        'open_interest': 'openInterest',
        # kline@... 格式特殊处理, 无需在此映射
    }

    def __init__(self, symbol: str, market_type: str, streams: list,
                 on_message_callback, quote_currency: str = None):
        """
        初始化Backpack WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'sol'.
            market_type (str): 市场类型, 'spot' or 'swap'.
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'trades', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdc'.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in ['spot', 'swap']:
            raise ValueError(f"Unsupported market_type: {market_type}. Must be 'spot' or 'swap'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")

        self.streams = streams
        self._ws = None
        # 数据转换器映射 (key是Backpack返回的事件类型 'e')
        self._data_transformers = {
            'depth': self._transform_depth,                 # 对应 'orderbook'
            'trade': self._transform_trade,                 # 对应 'trades'
            'kline': self._transform_kline,                 # 对应 'kline@...'
            'ticker': self._transform_ticker,               # 对应 'ticker'
            'bookTicker': self._transform_book_ticker,      # 对应 'best_orderbook'
            'markPrice': self._transform_mark_price,        # 对应 'mark_price'
            'liquidation': self._transform_liquidation,     # 对应 'liquidation'
            'openInterest': self._transform_open_interest,  # 对应 'open_interest'
        }

    # 构建交易对
    def _construct_symbol(self) -> str:
        """
        构建Backpack的完整交易对，例如 'SOL_USDC' 或 'SOL_USDC_PERP'。
        """
        quote = self.quote_currency if self.quote_currency else self._DEFAULT_QUOTE_CURRENCY
        base_symbol = f"{self.base_symbol}_{quote}".upper()

        if self.market_type == 'swap':
            return f"{base_symbol}_PERP"
        return base_symbol

    def get_kline_steam_name(self, interval: str = '1m') -> str:
        """获取K线流名称"""
        return f"kline.{interval}.{self._symbol}"

    def get_trade_steam_name(self) -> str:
        """获取交易流名称"""
        return f"trade.{self._symbol}"

    def get_book_ticker_steam_name(self) -> str:
        """获取最佳买卖价流名称"""
        return f"bookTicker.{self._symbol}"

    def get_depth_steam_name(self) -> str:
        """获取深度流名称"""
        return f"depth.{self._symbol}"

    def get_ticker_steam_name(self) -> str:
        """获取行情流名称"""
        return f"ticker.{self._symbol}"

    def get_mark_price_steam_name(self) -> str:
        """获取标记价格流名称"""
        return f"markPrice.{self._symbol}"

    def get_liquidation_steam_name(self) -> str:
        """获取清算流名称"""
        return "liquidation"

    def get_open_interest_steam_name(self) -> str:
        """获取持仓量流名称"""
        return f"openInterest.{self._symbol}"

    # 构建订阅流名称列表
    def _get_stream_names(self) -> list:
        """
        根据订阅流构建Backpack WebSocket的流名称列表。
        """
        stream_names = []
        for stream in self.streams:
            if stream.startswith('kline@'):
                interval = stream.split('@')[1]
                stream_names.append(f"kline.{interval}.{self._symbol}")
            else:
                backpack_stream = self._STREAM_NAME_MAP.get(stream)
                if backpack_stream:
                    if backpack_stream == 'liquidation':
                        stream_names.append(backpack_stream)
                    else:
                        stream_names.append(f"{backpack_stream}.{self._symbol}")
                else:
                    print(f"Warning: Unsupported stream name '{stream}' will be ignored.")

        if not stream_names:
            raise ValueError("No valid streams to subscribe to.")

        return stream_names

    # --- 数据转换函数 ---
    def _transform_depth(self, data: dict) -> dict:
        return {
            'type': 'orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'bids': data.get('b', [])[:5],
            'asks': data.get('a', [])[:5],
        }

    def _transform_trade(self, data: dict) -> dict:
        return {
            'type': 'trades',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'price': data.get('p'),
            'quantity': data.get('q'),
            'is_buyer_maker': data.get('m'),
            'trade_id': data.get('t'),
        }

    def _transform_kline(self, data: dict) -> dict:
        return {
            'type': 'kline',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'interval': self._extract_interval_from_stream(),
            'open': data.get('o'),
            'high': data.get('h'),
            'low': data.get('l'),
            'close': data.get('c'),
            'volume': data.get('v'),
            'is_closed': data.get('X', False),
        }

    def _transform_ticker(self, data: dict) -> dict:
        return {
            'type': 'ticker',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'open': data.get('o'),
            'close': data.get('c'),
            'high': data.get('h'),
            'low': data.get('l'),
            'volume': data.get('v'),
            'quote_volume': data.get('V'),
            'trades_count': data.get('n'),
        }

    def _transform_book_ticker(self, data: dict) -> dict:
        return {
            'type': 'best_orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'bid1_price': data.get('b'),
            'bid1_amount': data.get('B'),
            'ask1_price': data.get('a'),
            'ask1_amount': data.get('A'),
        }

    def _transform_mark_price(self, data: dict) -> dict:
        return {
            'type': 'mark_price',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'mark_price': data.get('p'),
            'funding_rate': data.get('f'),
            'index_price': data.get('i'),
            'next_funding_time': data.get('n', 0) // 1000,  # 转换微秒到毫秒
        }

    def _transform_liquidation(self, data: dict) -> dict:
        return {
            'type': 'liquidation',
            'symbol': data.get('s'),  # 使用数据中的symbol，因为liquidation是全局流
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'price': data.get('p'),
            'quantity': data.get('q'),
            'side': data.get('S'),
        }

    def _transform_open_interest(self, data: dict) -> dict:
        return {
            'type': 'open_interest',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E', 0) // 1000,  # 转换微秒到毫秒
            'open_interest': data.get('o'),
        }

    def _transform_unknown(self, data: dict) -> dict:
        """转换未知类型数据"""
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'raw_data': data
        }

    def _extract_interval_from_stream(self) -> str:
        """从流名称中提取时间间隔，用于kline数据"""
        # 这里需要根据订阅的流来确定间隔，暂时返回默认值
        for stream in self.streams:
            if stream.startswith('kline@'):
                return stream.split('@')[1]
        return '1m'

    async def _connect(self):
        """
        连接到Backpack的WebSocket服务器。
        """
        print(f"Connecting to {self._BASE_URI}...")
        self._ws = await websockets.connect(self._BASE_URI)
        print(f"Connected to {self._BASE_URI}.")

    async def _subscribe_streams(self):
        """
        订阅指定的数据流。
        """
        stream_names = self._get_stream_names()
        subscribe_message = {
            "method": "SUBSCRIBE",
            "params": stream_names
        }

        await self._ws.send(json.dumps(subscribe_message))
        print(f"Subscribed to streams: {stream_names}")

    async def _process_messages(self):
        """
        处理从Backpack WebSocket接收到的消息，并根据事件类型进行分发。
        """
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        # 先订阅流
        await self._subscribe_streams()

        async for message in self._ws:
            try:
                payload = json.loads(message)

                # 检查是否是流数据格式
                if 'stream' in payload and 'data' in payload:
                    data = payload['data']
                    event_type = data.get('e')

                    # 根据事件类型查找并使用对应的转换器
                    transformer = self._data_transformers.get(event_type)
                    if transformer:
                        transformed_data = transformer(data)
                    else:
                        print(f"Warning: Unknown event type '{event_type}' from Backpack, "
                              f"using unknown transformer")
                        transformed_data = self._transform_unknown(data)

                    # 将转换后的数据传递给回调函数
                    if self._on_message_callback:
                        self._on_message_callback(self, transformed_data)
                else:
                    # 处理订阅确认或其他非数据消息
                    if 'result' in payload:
                        print(f"Subscription result: {payload}")
                    else:
                        print(f"Received non-stream message: {payload}")

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")

    async def stop(self):
        """
        扩展基类方法，以确保WebSocket连接被关闭。
        """
        await super().stop()
        if self._ws:
            await self._ws.close()
            self._ws = None
        print(f"BackpackWssAdapter for {self._symbol} connection closed.")
