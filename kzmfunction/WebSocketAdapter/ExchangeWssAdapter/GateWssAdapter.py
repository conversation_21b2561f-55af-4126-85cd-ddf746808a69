# kzmfunction/WebSocketAdapter/GateWssAdapter.py

import asyncio
import json
import websockets
from ..WssAdapter import WssAdapter

class GateWssAdapter(WssAdapter):
    """
    Gate.io交易所的WebSocket适配器。
    支持现货和永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URIS = {
        'spot': "wss://api.gateio.ws/ws/v4/",
        'swap': "wss://fx-ws.gateio.ws/v4/ws/usdt"
    }
    _EXCHANGE_NAME = "gate"
    _DEFAULT_QUOTE_CURRENCY = 'usdt'

    # 标准化流名称到Gate流名称的映射
    _STREAM_NAME_MAP = {
        'best_orderbook': 'book_ticker',
        'orderbook': 'order_book',
        'trades': 'trades',
        'aggtrade': 'trades',  # Gate没有聚合交易，使用trades替代
        # kline@... 格式特殊处理, 无需在此映射
    }

    def __init__(self, symbol: str, market_type: str, streams: list, on_message_callback, quote_currency: str = None):
        """
        初始化Gate WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'btc'.
            market_type (str): 市场类型, 'spot' or 'swap'.
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'trades', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdt'.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in self._BASE_URIS:
            raise ValueError(f"Unsupported market_type: {market_type}. Must be 'spot' or 'swap'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")
        
        self.streams = streams
        self._ws = None
        
        # 数据转换器映射 (key是Gate返回的频道类型)
        self._data_transformers = {
            'spot.book_ticker': self._transform_book_ticker,
            'futures.book_ticker': self._transform_book_ticker,
            'spot.order_book': self._transform_depth_update,
            'futures.order_book': self._transform_depth_update,
            'spot.trades': self._transform_trade,
            'futures.trades': self._transform_trade,
            'spot.candlesticks': self._transform_kline,
            'futures.candlesticks': self._transform_kline,
        }
    
    # 构建交易对    
    def _construct_symbol(self) -> str:
        """
        构建Gate的完整交易对，例如 'BTC_USDT'。
        """
        quote = self.quote_currency if self.quote_currency else self._DEFAULT_QUOTE_CURRENCY
        return f"{self.base_symbol.upper()}_{quote.upper()}"

    def _get_channel_prefix(self) -> str:
        """获取频道前缀，根据市场类型"""
        return 'spot' if self.market_type == 'spot' else 'futures'

    def _build_subscriptions(self) -> list:
        """构建Gate的订阅消息列表"""
        subscriptions = []
        channel_prefix = self._get_channel_prefix()
        
        for stream in self.streams:
            payload_params = [self._symbol]
            if stream.startswith('kline@'):
                interval = stream.split('@')[1]
                gate_interval = self._get_gate_interval(interval)
                channel = f"{channel_prefix}.candlesticks"
                payload_params = [gate_interval, self._symbol]
            else:
                gate_stream = self._STREAM_NAME_MAP.get(stream)
                if not gate_stream:
                    print(f"Warning: Unsupported stream name '{stream}' will be ignored.")
                    continue
                
                channel = f"{channel_prefix}.{gate_stream}"
                if gate_stream == 'order_book':
                    if self.market_type == 'spot':
                        payload_params = [self._symbol, "5", "100ms"]
                    else: # swap
                        payload_params = [self._symbol, "20", "0"]
            
            subscriptions.append({"channel": channel, "payload": payload_params})

        return subscriptions

    def _get_gate_interval(self, interval: str) -> str:
        """将标准间隔转换为Gate间隔格式"""
        interval_map = {'1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m', '1h': '1h', '4h': '4h', '8h': '8h', '1d': '1d', '7d': '7d'}
        return interval_map.get(interval, '1m')

    # --- 数据转换函数 ---
    def _transform_depth_update(self, data: dict) -> dict:
        """转换Gate订单簿数据为标准格式"""
        result = data.get('result', {})
        if not result or not isinstance(result, dict):
            return {}
        return {
            'type': 'orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(result.get('t', 0)),
            'bids': result.get('bids', [])[:5],
            'asks': result.get('asks', [])[:5],
        }

    def _transform_trade(self, data: dict) -> list:
        """转换Gate交易数据为标准格式列表"""
        results = data.get('result', [])
        if not isinstance(results, list):
            return []
        
        transformed_trades = []
        for trade in results:
            if self.market_type == 'spot':
                quantity = trade.get('amount')
                is_buyer_maker = trade.get('side') == 'sell'
            else: # swap
                quantity = abs(trade.get('size', 0))
                is_buyer_maker = None # Not available in public futures trades
            
            transformed_trades.append({
                'type': 'trades',
                'symbol': self._symbol,
                'market_type': self.market_type,
                'timestamp_ms': int(trade.get('create_time_ms', 0)),
                'price': trade.get('price'),
                'quantity': quantity,
                'is_buyer_maker': is_buyer_maker,
            })
        return transformed_trades

    def _transform_kline(self, data: dict) -> list:
        """转换Gate K线数据为标准格式列表"""
        results = data.get('result', [])
        if not isinstance(results, list):
            return []

        transformed_klines = []
        for kline_data in results:
            if self.market_type == 'spot':
                # Spot: [t, v, c, h, l, o]
                t, v, c, h, l, o = kline_data
                kline = {'t': t, 'v': v, 'c': c, 'h': h, 'l': l, 'o': o}
            else: # Swap: {"t":..., "v":...}
                kline = kline_data

            transformed_klines.append({
                'type': 'kline',
                'symbol': self._symbol,
                'market_type': self.market_type,
                'timestamp_ms': int(kline.get('t', 0) * 1000),
                'interval': data.get('payload', ['1m'])[0],
                'open': kline.get('o'),
                'high': kline.get('h'),
                'low': kline.get('l'),
                'close': kline.get('c'),
                'volume': kline.get('v'),
            })
        return transformed_klines
    
    def _transform_book_ticker(self, data: dict) -> dict:
        """转换Gate最佳买卖价数据为标准格式"""
        result = data.get('result', {})
        if not result or not isinstance(result, dict):
            return {}
            
        return {
            'type': 'best_orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(result.get('t', 0)),
            'bid1_price': result.get('b'),
            'bid1_amount': result.get('B'),
            'ask1_price': result.get('a'),
            'ask1_amount': result.get('A'),
        }

    def _transform_unknown(self, data: dict) -> dict:
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
        }

    async def _connect(self):
        """连接到Gate的WebSocket服务器"""
        stream_url = self._BASE_URIS[self.market_type]
        print(f"Connecting to {stream_url}...")
        self._ws = await websockets.connect(stream_url)
        print(f"Connected to {stream_url}.")

    async def _subscribe(self):
        """发送订阅消息"""
        subscriptions = self._build_subscriptions()
        
        for subscription in subscriptions:
            subscribe_message = {
                "time": int(asyncio.get_event_loop().time()),
                "channel": subscription["channel"],
                "event": "subscribe",
                "payload": subscription["payload"]
            }
            await self._ws.send(json.dumps(subscribe_message))
            print(f"Subscription message sent: {subscribe_message}")

    async def _process_messages(self):
        """处理从Gate WebSocket接收到的消息"""
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        await self._subscribe()

        async for message in self._ws:
            try:
                payload = json.loads(message)
                
                if payload.get('event') == 'subscribe':
                    if payload.get('error'):
                        print(f"Subscription failed: {payload}")
                    else:
                        print(f"Subscription confirmed: {payload}")
                    continue
                
                channel = payload.get('channel')
                if channel:
                    transformer = self._data_transformers.get(channel)
                    if transformer:
                        transformed_data = transformer(payload)
                        
                        if isinstance(transformed_data, list):
                            for item in transformed_data:
                                if self._on_message_callback and item:
                                    self._on_message_callback(self, item)
                        elif self._on_message_callback and transformed_data:
                            self._on_message_callback(self, transformed_data)
                    else:
                        if self._on_message_callback:
                            self._on_message_callback(self, self._transform_unknown(payload))

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")

    async def _run(self):
        """运行WebSocket连接和消息处理"""
        try:
            await self._connect()
            await self._process_messages()
        except websockets.exceptions.ConnectionClosed:
            print("Connection closed, attempting to reconnect...")
            await asyncio.sleep(1)
            await self._run()
        except Exception as e:
            print(f"Error in WebSocket connection: {e}")
            await asyncio.sleep(1)
            await self._run()

    async def stop(self):
        """停止WebSocket连接"""
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
            self._task = None
        
        if self._ws:
            await self._ws.close()
            self._ws = None