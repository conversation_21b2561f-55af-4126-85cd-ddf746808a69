#!/usr/bin/env python3
"""
测试BackpackWssAdapter的简单示例
"""

import asyncio
import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from BackpackWssAdapter import BackpackWssAdapter


def on_message_callback(adapter, data):
    """处理接收到的WebSocket消息的回调函数"""
    print(f"[{adapter._EXCHANGE_NAME}] {data['type']}: {data}")


async def test_backpack_adapter():
    """测试Backpack WebSocket适配器"""
    
    # 创建适配器实例
    adapter = BackpackWssAdapter(
        symbol='sol',
        market_type='spot',
        streams=['best_orderbook', 'trades'],
        on_message_callback=on_message_callback,
        quote_currency='usdc'
    )
    
    print(f"创建Backpack适配器，交易对: {adapter._symbol}")
    print(f"订阅流: {adapter.streams}")
    
    try:
        # 启动适配器
        await adapter.start()
        
        # 运行30秒
        print("开始接收数据，运行30秒...")
        await asyncio.sleep(30)
        
    except KeyboardInterrupt:
        print("收到中断信号，正在停止...")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 停止适配器
        await adapter.stop()
        print("适配器已停止")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_backpack_adapter())
