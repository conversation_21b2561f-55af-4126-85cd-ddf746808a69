
# kzmfunction/WebSocketAdapter/OkxWssAdapter.py

import asyncio
import json
import websockets
from ..WssAdapter import WssAdapter

class OkxWssAdapter(WssAdapter):
    """
    OKX交易所的WebSocket适配器。
    支持现货和永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URIS = {
        'spot': "wss://ws.okx.com:8443/ws/v5/public",
        'swap': "wss://ws.okx.com:8443/ws/v5/public"
    }
    _EXCHANGE_NAME = "okx"
    _DEFAULT_QUOTE_CURRENCY = 'usdt'

    _STREAM_NAME_MAP = {
        'best_orderbook': 'tickers',
        'orderbook': 'books5',
        'trades': 'trades',
        'kline@1m': 'candle1m',
        'aggtrade': 'trades', # OKX没有aggTrade，使用trades替代
    }

    def __init__(self, symbol: str, market_type: str, streams: list, on_message_callback, quote_currency: str = None):
        """
        初始化OKX WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'btc'.
            market_type (str): 市场类型, 'spot' or 'swap'.
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'aggtrade', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdt'.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in self._BASE_URIS:
            raise ValueError(f"Unsupported market_type: {market_type}. Must be 'spot' or 'swap'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")
        
        self.streams = streams
        self._ws = None
        
        self._data_transformers = {
            'books5': self._transform_depth_update,
            'trades': self._transform_trade,
            'candle1m': self._transform_kline,
            'tickers': self._transform_book_ticker,
        }
    
    def _construct_symbol(self) -> str:
        """
        构建OKX的完整交易对，例如 'BTC-USDT'。
        """
        quote = self.quote_currency if self.quote_currency else self._DEFAULT_QUOTE_CURRENCY
        return f"{self.base_symbol.upper()}-{quote.upper()}"

    def _get_subscription_args(self) -> list:
        """
        构建OKX的订阅参数。
        """
        args = []
        for stream in self.streams:
            channel = self._STREAM_NAME_MAP.get(stream)
            if channel:
                instId = self._construct_symbol()
                if self.market_type == 'swap':
                    instId += '-SWAP'
                args.append({"channel": channel, "instId": instId})
            else:
                print(f"Warning: Unsupported stream name '{stream}' will be ignored.")
        return args

    # --- 数据转换函数 ---
    def _transform_depth_update(self, data: dict) -> dict:
        return {
            'type': 'orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(data.get('ts')),
            'bids': data.get('bids', [])[:5],
            'asks': data.get('asks', [])[:5],
        }

    def _transform_trade(self, data: dict) -> dict:
        return {
            'type': 'trade',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(data.get('ts')),
            'price': data.get('px'),
            'quantity': data.get('sz'),
            'is_buyer_maker': data.get('side') == 'sell', # 'buy' is taker, 'sell' is maker
        }

    def _transform_kline(self, data: list) -> dict:
        return {
            'type': 'kline',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(data[0]),
            'interval': '1m',
            'open': data[1],
            'high': data[2],
            'low': data[3],
            'close': data[4],
            'volume': data[5],
        }
    
    def _transform_book_ticker(self, data: dict) -> dict:
        return {
            'type': 'best_orderbook',
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': int(data.get('ts')),
            'bid1_price': data.get('bidPx'),
            'bid1_amount': data.get('bidSz'),
            'ask1_price': data.get('askPx'),
            'ask1_amount': data.get('askSz'),
        }

    def _transform_unknown(self, data: dict) -> dict:
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'raw_data': data
        }

    async def _connect(self):
        """
        连接到OKX的WebSocket服务器。
        """
        stream_url = self._BASE_URIS[self.market_type]
        print(f"Connecting to {stream_url}...")
        self._ws = await websockets.connect(stream_url)
        print(f"Connected to {stream_url}.")

        args = self._get_subscription_args()
        if not args:
            raise ValueError("No valid streams to subscribe to.")

        sub_message = {
            "op": "subscribe",
            "args": args
        }
        await self._ws.send(json.dumps(sub_message))
        print(f"Sent subscription request: {json.dumps(sub_message)}")

    async def _process_messages(self):
        """
        处理从OKX WebSocket接收到的消息。
        """
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        async for message in self._ws:
            # Public channel sends plaintext, no decompression needed.
            if message == 'ping':
                await self._ws.send('pong')
                continue

            try:
                # The message is expected to be a JSON string.
                payload = json.loads(message)
                
                if 'event' in payload and payload['event'] == 'error':
                    print(f"Error message from server: {payload}")
                    continue

                if 'arg' in payload and 'data' in payload:
                    arg = payload['arg']
                    channel = arg.get('channel')
                    
                    transformer = self._data_transformers.get(channel)
                    if transformer:
                        for item in payload['data']:
                            transformed_data = transformer(item)
                            if self._on_message_callback:
                                self._on_message_callback(self, transformed_data)
                    else:
                        transformed_data = self._transform_unknown(payload)
                        if self._on_message_callback:
                            self._on_message_callback(self, transformed_data)

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")

    async def stop(self):
        """
        扩展基类方法，以确保WebSocket连接被关闭。
        """
        await super().stop()
        if self._ws:
            args = self._get_subscription_args()
            if args:
                unsub_message = {
                    "op": "unsubscribe",
                    "args": args
                }
                await self._ws.send(json.dumps(unsub_message))
                print(f"Sent unsubscription request: {json.dumps(unsub_message)}")
            await self._ws.close()
            self._ws = None
        print(f"OkxWssAdapter for {self._symbol} connection closed.")

