# kzmfunction/WebSocketAdapter/BinanceWssAdapter.py

# import asyncio
import json
import websockets
from ..WssAdapter import WssAdapter

class BinanceWssAdapter(WssAdapter):
    """
    币安交易所的WebSocket适配器。
    支持现货和永续合约，能自动构建交易对，并处理多种数据流。
    """

    _BASE_URIS = {
        'spot': "wss://stream.binance.com:9443",
        'swap': "wss://fstream.binance.com"
    }
    _EXCHANGE_NAME = "binance"
    _DEFAULT_QUOTE_CURRENCY = 'usdt'

    # 新增: 标准化流名称到Binance流名称的映射
    _STREAM_NAME_MAP = {
        'best_orderbook': 'bookTicker',
        'orderbook': 'depth',
        'trades': 'trade',
        'aggtrade': 'aggTrade',
        # kline@... 格式特殊处理, 无需在此映射
    }

    def __init__(self, symbol: str, market_type: str, streams: list, on_message_callback, quote_currency: str = None):
        """
        初始化币安WebSocket适配器。

        Args:
            symbol (str): 基础币种, e.g., 'btc'.
            market_type (str): 市场类型, 'spot' or 'swap'.
            streams (list): 需要订阅的标准化数据流列表, e.g., ['orderbook', 'aggtrade', 'kline@1m'].
            on_message_callback (callable): 接收到消息时调用的回调函数。
            quote_currency (str, optional): 计价货币, e.g., 'usdc'. Defaults to 'usdt'.
        """
        super().__init__(symbol, market_type, on_message_callback, quote_currency)
        if market_type not in self._BASE_URIS:
            raise ValueError(f"Unsupported market_type: {market_type}. Must be 'spot' or 'swap'.")
        if not streams:
            raise ValueError("The 'streams' parameter cannot be empty.")
        
        self.streams = streams
        self._ws = None
        
        # 数据转换器映射 (key是Binance返回的事件类型 'e')
        self._data_transformers = {
            'depthUpdate': self._transform_depth_update, # 对应 'orderbook'
            'aggTrade': self._transform_agg_trade,       # 对应 'aggtrade'
            'kline': self._transform_kline,             # 对应 'kline@...'
            'trade': self._transform_trade,             # 对应 'trades'
            'bookTicker': self._transform_book_ticker,  # 对应 'best_orderbook'
        }
    
    # 构建交易对    
    def _construct_symbol(self) -> str:
        """
        构建币安的完整交易对，例如 'btcusdt'。
        """
        quote = self.quote_currency if self.quote_currency else self._DEFAULT_QUOTE_CURRENCY
        return f"{self.base_symbol}{quote}"

    def get_kline_steam_name(self, interval: str = '1m') -> str:
        return f"{self._symbol}@kline_{interval}"

    def get_trade_steam_name(self) -> str:
        return f"{self._symbol}@trade"

    def get_book_ticker_steam_name(self) -> str:
        return f"{self._symbol}@bookTicker"

    def get_depth_steam_name(self, level: int = 5) -> str:
        return f"{self._symbol}@depth{level}"

    # 构建订阅URL(交易对，类别(spot,swap))
    def _get_stream_url(self) -> str:
        """
        根据市场类型和订阅流构建币安WebSocket的订阅URL。
        """
        base_uri = self._BASE_URIS[self.market_type]
        
        # 将标准流名称转换为Binance的流名称
        stream_names = []
        for stream in self.streams:
            if stream.startswith('kline@'):
                stream_names.append(f"{self._symbol}@{stream}")
            else:
                binance_stream = self._STREAM_NAME_MAP.get(stream)
                if binance_stream:
                    stream_names.append(f"{self._symbol}@{binance_stream}")
                else:
                    print(f"Warning: Unsupported stream name '{stream}' will be ignored.")

        if not stream_names:
            raise ValueError("No valid streams to subscribe to.")

        return f"{base_uri}/stream?streams={'/'.join(stream_names)}"

    # --- 数据转换函数 ---
    def _transform_depth_update(self, data: dict) -> dict:
        return {
            'type': 'orderbook',
            # 'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E'),
            'bids': data.get('b', [])[:5],
            'asks': data.get('a', [])[:5],
            # 'raw_data': data
        }

    def _transform_agg_trade(self, data: dict) -> dict:
        return {
            'type': 'trades',
            # 'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('T'),
            'price': data.get('p'),
            'quantity': data.get('q'),
            'is_buyer_maker': data.get('m'),
            # 'trade_id': data.get('a'),
            # 'raw_data': data
        }

    def _transform_kline(self, data: dict) -> dict:
        kline_data = data.get('k', {})
        return {
            'type': 'kline',
            # 'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': kline_data.get('t'),
            'interval': kline_data.get('i'),
            'open': kline_data.get('o'),
            'high': kline_data.get('h'),
            'low': kline_data.get('l'),
            'close': kline_data.get('c'),
            'volume': kline_data.get('v'),
            # 'raw_data': data
        }
    
    def _transform_trade(self, data: dict) -> dict:
        return {
            'type': 'trade', # 标准化名称
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('T'),
            'price': data.get('p'),
            'quantity': data.get('q'),
            'is_buyer_maker': data.get('m'),
        }

    def _transform_book_ticker(self, data: dict) -> dict:
        return {
            'type': 'best_orderbook', # 标准化名称
            'symbol': self._symbol,
            'market_type': self.market_type,
            'timestamp_ms': data.get('E'),
            'bid1_price': data.get('b'),
            'bid1_amount': data.get('B'),
            'ask1_price': data.get('a'),
            'ask1_amount': data.get('A'),
        }

    def _transform_unknown(self, data: dict) -> dict:
        return {
            'type': 'unknown',
            'exchange': self._EXCHANGE_NAME,
            'symbol': self._symbol,
            'market_type': self.market_type,
            # 'raw_data': data
        }

    async def _connect(self):
        """
        连接到币安的WebSocket服务器。
        """
        stream_url = self._get_stream_url()
        print(f"Connecting to {stream_url}...")
        self._ws = await websockets.connect(stream_url)
        print(f"Connected to {stream_url}.")

    async def _process_messages(self):
        """
        处理从币安WebSocket接收到的消息，并根据事件类型进行分发。
        """
        if not self._ws:
            raise ConnectionError("WebSocket connection is not established.")

        async for message in self._ws:
            try:
                payload = json.loads(message)
                
                # 检查是否是多数据流格式
                if 'stream' in payload and 'data' in payload:
                    data = payload['data']
                    event_type = data.get('e')

                    # 如果没有'e'字段，根据数据结构推断事件类型
                    if event_type is None:
                        # bookTicker数据格式：{'u': ..., 's': ..., 'b': ..., 'B': ..., 'a': ..., 'A': ...}
                        if all(key in data for key in ['u', 's', 'b', 'B', 'a', 'A']):
                            event_type = 'bookTicker'
                        else:
                            print(f"Debug: Unknown Binance data format: {data}")
                            print(f"Debug: Available keys in data: {list(data.keys())}")

                    # 根据事件类型查找并使用对应的转换器
                    transformer = self._data_transformers.get(event_type)
                    if transformer:
                        transformed_data = transformer(data)
                    else:
                        print(f"Warning: Unknown event type '{event_type}' from Binance, using unknown transformer")
                        transformed_data = self._transform_unknown(data)
                    
                    # 将转换后的数据传递给回调函数
                    if self._on_message_callback:
                        self._on_message_callback(self, transformed_data)
                else:
                    # 处理非流式（例如，请求响应）或未知格式的消息
                    transformed_data = self._transform_unknown(payload)
                    if self._on_message_callback:
                        self._on_message_callback(self, transformed_data)

            except json.JSONDecodeError:
                print(f"Error decoding JSON from message: {message}")
            except Exception as e:
                print(f"An error occurred while processing message: {e}")

    async def stop(self):
        """
        扩展基类方法，以确保WebSocket连接被关闭。
        """
        await super().stop()
        if self._ws:
            await self._ws.close()
            self._ws = None
        print(f"BinanceWssAdapter for {self._symbol} connection closed.")