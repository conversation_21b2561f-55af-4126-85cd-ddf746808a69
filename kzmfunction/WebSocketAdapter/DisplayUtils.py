#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
显示工具模块
提供颜色输出
"""


class Colors:
    """ANSI颜色代码"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # 重置颜色


def print_colored(text: str, color: str = Colors.WHITE, bold: bool = False):
    """打印彩色文字"""
    style = Colors.BOLD if bold else ''
    print(f"{color}{style}{text}{Colors.END}")


def print_red(text: str):
    """打印红色文字"""
    print_colored(text, Colors.RED, bold=True)


def print_green(text: str):
    """打印绿色文字"""
    print_colored(text, Colors.GREEN, bold=True)


def print_yellow(text: str):
    """打印黄色文字"""
    print_colored(text, Colors.YELLOW, bold=True)


def print_blue(text: str):
    """打印蓝色文字"""
    print_colored(text, Colors.BLUE, bold=True)


def print_trading_status(exchange_1_name: str, exchange_2_name: str, 
                        exchange_1_price: float, exchange_2_price: float, 
                        premium: float, threshold: float):
    """打印交易状态信息"""
    if abs(premium) >= threshold:
        print_green(f"🚨 溢价超过阈值! {exchange_1_name}:{exchange_1_price:.4f}, "
                   f"{exchange_2_name}:{exchange_2_price:.4f}, 溢价:{premium:.4f}")
    else:
        print_yellow(f"💰 当前溢价: {exchange_1_name}:{exchange_1_price:.4f}, "
                    f"{exchange_2_name}:{exchange_2_price:.4f}, 溢价:{premium:.4f}")


def print_order_result(success: bool, order_info: dict, exchange_name: str):
    """打印下单结果"""
    if success:
        print_green(f"✅ {exchange_name} 下单成功: {order_info}")
    else:
        print_red(f"❌ {exchange_name} 下单失败: {order_info}")