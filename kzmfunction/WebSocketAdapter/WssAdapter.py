# kzmfunction/WebSocketAdapter/WssAdapter.py

import asyncio

class WssAdapter:
    def __init__(self, symbol: str, market_type: str, on_message_callback, quote_currency: str = None):
        self.base_symbol = symbol
        self.market_type = market_type
        self._on_message_callback = on_message_callback
        self.quote_currency = quote_currency
        self._symbol = self._construct_symbol()
        self._task = None

    def _construct_symbol(self) -> str:
        raise NotImplementedError

    async def _connect(self):
        raise NotImplementedError

    async def _process_messages(self):
        raise NotImplementedError

    async def start(self):
        if self._task is None:
            self._task = asyncio.create_task(self._run())

    async def _run(self):
        try:
            await self._connect()
            await self._process_messages()
        except Exception as e:
            print(f"Error in WssAdapter for {self.base_symbol}: {e}")
        finally:
            print(f"WssAdapter for {self.base_symbol} stopped.")

    async def stop(self):
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
            self._task = None
            print(f"WssAdapter for {self.base_symbol} task cancelled.")
