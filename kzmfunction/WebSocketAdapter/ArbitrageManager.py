#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
套利管理器模块
提供完整的WebSocket套利交易管理功能
"""

import asyncio
import sys
from typing import Dict, Any, Callable, Optional
from traceback import format_exc

from .WebSocketCore import WSConfig, OrderbookCache, WebSocketManager, create_wss_adapter
from .CallbackHandlers import create_callback_handler


class ArbitrageManager:
    """WebSocket套利交易管理器"""
    
    def __init__(self, 
                 exchange_1_name: str,
                 exchange_2_name: str,
                 symbol: str,
                 r_threshold: float,
                 trading_logic_callback: Callable,
                 balance_monitor_callback: Optional[Callable] = None,
                 ws_config: Optional[WSConfig] = None):
        """
        初始化套利管理器
        
        Args:
            exchange_1_name: 交易所1名称
            exchange_2_name: 交易所2名称  
            symbol: 交易对
            r_threshold: 价差阈值
            trading_logic_callback: 交易逻辑回调函数
            balance_monitor_callback: 余额监控回调函数（可选）
            ws_config: WebSocket配置（可选）
        """
        self.exchange_1_name = exchange_1_name
        self.exchange_2_name = exchange_2_name
        self.symbol = symbol
        self.r_threshold = r_threshold
        self.trading_logic_callback = trading_logic_callback
        self.balance_monitor_callback = balance_monitor_callback
        
        # WebSocket组件
        self.ws_config = ws_config or WSConfig()
        self.orderbook_cache = OrderbookCache()
        self.ws_manager = WebSocketManager(self.ws_config)
        
        # 交易控制
        self.trading_enabled = False
        self.trade_cooldown = 1.0
        self.callback_handler = None
        
        # 任务管理
        self.tasks = []
        
    def set_trading_enabled(self, enabled: bool):
        """设置交易状态"""
        self.trading_enabled = enabled
        
    def get_trading_enabled(self) -> bool:
        """获取交易状态"""
        return self.trading_enabled
    
    async def trigger_trading_wrapper(self, exchange_1_data, exchange_2_data):
        """交易逻辑包装函数"""
        try:
            await self.trading_logic_callback(exchange_1_data, exchange_2_data)
        except Exception as e:
            print(f"Trading logic error: {e}")
            print(format_exc())
    
    def on_orderbook_message(self, adapter, data: dict):
        """订单簿消息回调"""
        if self.callback_handler:
            self.callback_handler.handle_orderbook_update(adapter, data)
    
    async def setup_websockets(self):
        """设置WebSocket连接"""
        try:
            # 初始化回调处理器
            self.callback_handler = create_callback_handler(
                'arbitrage',
                self.orderbook_cache,
                exchange_1_name=self.exchange_1_name,
                exchange_2_name=self.exchange_2_name,
                r_threshold=self.r_threshold,
                trading_enabled_callback=self.get_trading_enabled,
                trigger_trading_callback=self.trigger_trading_wrapper,
                trade_cooldown=self.trade_cooldown
            )
            
            # 创建WebSocket适配器
            exchange_1_adapter = create_wss_adapter(
                self.exchange_1_name, 
                self.symbol, 
                self.on_orderbook_message
            )
            await self.ws_manager.add_adapter(self.exchange_1_name, exchange_1_adapter)

            exchange_2_adapter = create_wss_adapter(
                self.exchange_2_name, 
                self.symbol, 
                self.on_orderbook_message
            )
            await self.ws_manager.add_adapter(self.exchange_2_name, exchange_2_adapter)

            print(f"WebSocket adapters created for {self.exchange_1_name} and {self.exchange_2_name}")

        except Exception as e:
            print(f"Error setting up WebSockets: {e}")
            raise
    
    async def start(self):
        """启动套利管理器"""
        try:
            # 设置WebSocket连接
            await self.setup_websockets()
            
            # 启动WebSocket连接
            await self.ws_manager.start_all()
            
            print("WebSocket connections started. Trading logic will be triggered by real-time data.")
            print("Press Ctrl+C to stop the program.")
            
            # 创建任务列表
            self.tasks = []
            
            # 添加余额监控任务（如果提供）
            if self.balance_monitor_callback:
                self.tasks.append(asyncio.create_task(self.balance_monitor_callback()))
                print('启动账户余额监控程序...')
            
            # 保持程序运行，等待WebSocket数据触发交易
            while self.trading_enabled:
                await asyncio.sleep(1)
            
            print("Trading completed or stopped.")
            
        except KeyboardInterrupt:
            print("Program interrupted by user.")
        except Exception as e:
            print(f"Arbitrage manager error: {e}")
            print(format_exc())
        finally:
            await self.stop()
    
    async def stop(self):
        """停止套利管理器"""
        try:
            # 停止交易
            self.trading_enabled = False
            
            # 取消所有任务
            for task in self.tasks:
                if not task.done():
                    task.cancel()
            
            # 等待任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)
            
            # 停止WebSocket连接
            await self.ws_manager.stop_all()
            
            print("Arbitrage manager stopped.")
            
        except Exception as e:
            print(f"Error stopping arbitrage manager: {e}")
    
    def get_orderbook_cache(self) -> OrderbookCache:
        """获取订单簿缓存"""
        return self.orderbook_cache
    
    def get_latest_data(self):
        """获取最新的订单簿数据"""
        return (
            self.orderbook_cache.get(self.exchange_1_name),
            self.orderbook_cache.get(self.exchange_2_name)
        )
    
    def get_fresh_data(self, timeout: float = 60.0):
        """获取新鲜的订单簿数据"""
        return self.orderbook_cache.get_both_fresh(
            self.exchange_1_name, 
            self.exchange_2_name, 
            timeout
        )


class SimpleArbitrageManager(ArbitrageManager):
    """简化版套利管理器（仅显示数据，不进行交易）"""
    
    async def setup_websockets(self):
        """设置WebSocket连接（仅显示模式）"""
        try:
            # 使用简单显示回调处理器
            self.callback_handler = create_callback_handler(
                'display',
                self.orderbook_cache
            )
            
            # 创建WebSocket适配器
            exchange_1_adapter = create_wss_adapter(
                self.exchange_1_name, 
                self.symbol, 
                self.on_orderbook_message
            )
            await self.ws_manager.add_adapter(self.exchange_1_name, exchange_1_adapter)

            exchange_2_adapter = create_wss_adapter(
                self.exchange_2_name, 
                self.symbol, 
                self.on_orderbook_message
            )
            await self.ws_manager.add_adapter(self.exchange_2_name, exchange_2_adapter)

            print(f"WebSocket adapters created for {self.exchange_1_name} and {self.exchange_2_name} (Display mode)")

        except Exception as e:
            print(f"Error setting up WebSockets: {e}")
            raise
    
    async def start(self):
        """启动简化版套利管理器"""
        try:
            await self.setup_websockets()
            await self.ws_manager.start_all()
            
            print("WebSocket connections started in display mode.")
            print("Press Ctrl+C to stop the program.")
            
            # 持续运行直到手动停止
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("Program interrupted by user.")
        finally:
            await self.stop()
