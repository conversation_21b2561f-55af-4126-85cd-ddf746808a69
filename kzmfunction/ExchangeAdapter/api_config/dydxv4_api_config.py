"""
dydx api_key 和 api_secret 配置文件

"""

api_config = {
    # eth地址 api账号
    'dydx-api': {
        'dydx_address': 'dydx1x0smnj6djtkx9hz62ccvd8x4egt7renv2vpxmk',
        'mnemonic': 'leaf home execute spawn tail morning manage bonus worth movie pond catalog much ship short reflect release save banana juice album seek situate feature',
        'stark_private_key': '',
        # 注意：chrome浏览器中获取到的stark_public_key_y_coordinate是不带0x中的x，需要手动添加x
        'stark_public_key_y_coordinate': '',
        'api_key_credentials': {'key': '', 'secret': '', 'passphrase': ''},
        }
}
