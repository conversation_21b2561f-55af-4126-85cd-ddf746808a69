# region =======================================备注说明
"""
备注说明：
    Lighter API 配置文件
    
    配置说明：
    - private_key: 私钥，用于签名交易
    - account_index: 账户索引
    - api_key_index: API密钥索引
    - address: 账户地址
"""
# endregion =======================================备注说明

api_config = {
    'lighter-4': {
        'private_key': '0x79c85280c6c3428cb289bf381027eb1c3bd35c0fc04d0cdcd6c93494a2d9d94b',  # 请填入实际的私钥
        'account_index': 31499,     # 请填入实际的账户索引
        'api_key_index': 1,      # API密钥索引
        'address': '0x69E525C5498cbd2c796266FA45687aEfC9B4d3ee'       # 请填入实际的账户地址
    },
    'lighter-1': {
        'private_key': '0x...',  # 请填入实际的私钥
        'account_index': 66,     # 请填入实际的账户索引
        'api_key_index': 1,      # API密钥索引
        'address': '0x...'       # 请填入实际的账户地址
    }
}
