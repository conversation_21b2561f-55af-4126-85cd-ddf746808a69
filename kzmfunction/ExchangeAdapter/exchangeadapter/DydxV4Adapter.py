# region ==============================================备注说明
"""
备注说明：
https://docs.dydx.exchange/api_integration-clients/indexer_client
pip install v4-client-py
pip install v4-proto
pip install dydx-v4-client
"""
# endregion


# region ==============================================import
import sys
import random
sys.path.extend(['/home/<USER>/ssh/kzmfunction/ssh'])
from traceback import format_exc
from math import ceil, log
from dydx_v4_client.node.client import NodeClient
from dydx_v4_client.indexer.rest.indexer_client import IndexerClient
from dydx_v4_client.network import make_mainnet
from dydx_v4_client import MAX_CLIENT_ID, OrderFlags
from dydx_v4_client.indexer.rest.constants import OrderType
from dydx_v4_client.wallet import Wallet
from dydx_v4_client.node.market import Market, since_now
from v4_proto.dydxprotocol.clob.order_pb2 import Order
from .ExchangeAdapter import ExchangeAdapter
from .api_config import dydxv4_api_config
import asyncio
# endregion ==============================================import


class DydxV4Adapter(ExchangeAdapter):
    def __init__(self):
        raise RuntimeError("Please use DydxV4Adapter.create() instead")

    @classmethod
    async def create(cls, account='dydx-api', address=None):
        self = cls.__new__(cls)
        self._api_config = dydxv4_api_config.api_config[account]
        self.account = account
        self.exchange_name = 'dydx_v4'
        self.address = address if address else self._api_config['dydx_address']
        NETWORK = make_mainnet(
            node_url="dydx-grpc.publicnode.com",
            rest_indexer="https://indexer.dydx.trade/",
            websocket_indexer="wss://indexer.dydx.trade/"
        )
        self.exchange = IndexerClient(host=NETWORK.rest_indexer)
        self.node = await NodeClient.connect(NETWORK.node)
        self.wallet = await Wallet.from_mnemonic(self.node, self._api_config['mnemonic'], self.address)
        subaccounts_response = await self.exchange.account.get_subaccounts(self.address)
        self.subaccount_number = subaccounts_response['subaccounts'][0]['subaccountNumber']
        return self

    # region =============================================u swap
    # TODO:获取最新价格
    async def get_swap_latest_price(self, symbol):
        pass

    # 获取swap盘口买1价
    async def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            info = await self.exchange.markets.get_perpetual_market_orderbook(market=symbol.upper() + '-USD')
            return float(info['bids'][0]['price'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    async def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            info = await self.exchange.markets.get_perpetual_market_orderbook(market=symbol.upper() + '-USD')
            return float(info['asks'][0]['price'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口最佳盘口
    async def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60972.0', '0.85664'], 'ask': ['60973.0', '0.65602']}
        """
        raw_orderbook = await self.exchange.markets.get_perpetual_market_orderbook(market=symbol.upper() + '-USD')
        return {
            'bid': [raw_orderbook['bids'][0]['price'], raw_orderbook['bids'][0]['size']],
            'ask': [raw_orderbook['asks'][0]['price'], raw_orderbook['asks'][0]['size']]
        }

    # 获取swap盘口盘口
    async def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        """
        try:
            raw_orderbook = await self.exchange.markets.get_perpetual_market_orderbook(market=symbol.upper() + '-USD')
            return {
                'symbol': symbol,
                'bids': [[i['price'], i['size']] for i in raw_orderbook['bids'][:limit]],
                'asks': [[i['price'], i['size']] for i in raw_orderbook['asks'][:limit]]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    async def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            info = await self.get_swap_instruments_info(symbol)
            return float(info['nextFundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    async def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大24(24小时)，修改下面day=1可获取更多
        :return: list越往后时间越靠近现在
        {
          "symbol": "btc",
          "funding_rate": [
            "0.0000125",
            "0.0000125",
            "0.00001366",
            "0.00002802",
            "0.0000125"
          ],
          "funding_time": [
            1723338000013,
            1723341600009,
            1723345200075,
            1723348800143,
            1723352400017
          ]
        }
        """
        try:
            funding_rates = await self.exchange.markets.get_perpetual_market_historical_funding(market=symbol.upper() + '-USD')
            return {
                'symbol': symbol,
                'funding_rate': [i['rate'] for i in funding_rates['historicalFunding'][:limit]],
                'funding_time': [i['effectiveAt'] for i in funding_rates['historicalFunding'][:limit]]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usd)
    async def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            info = await self.get_swap_instruments_info(symbol)
            return float(info['openInterest']) * float(info['oraclePrice'])
        except:
            print(format_exc())
            return None

    # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    async def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            clobPairId: "31",
            ticker: "SUI-USD",
            status: "ACTIVE",
            oraclePrice: "3.170453408",
            priceChange24H: "0.337664212",
            volume24H: "8026.185",
            trades24H: 262,
            nextFundingRate: "0",
            initialMarginFraction: "0.1",
            maintenanceMarginFraction: "0.05",
            openInterest: "193950",
            atomicResolution: -5,
            quantumConversionExponent: -9,
            tickSize: "0.0001",
            stepSize: "10",     # 数量精度，以10为一个单位
            stepBaseQuantums: 1000000,
            subticksPerTick: 1000000,
            marketType: "CROSS",
            openInterestLowerCap: "20000000",
            openInterestUpperCap: "50000000",
            baseOpenInterest: "187450"
            }
        """
        _info = await self.exchange.markets.get_perpetual_markets()
        for key, value in _info['markets'].items():
            if symbol.upper() + '-USD' == key:
                return value

        # 获取下单价格精度

    # 获取下单价格精度
    async def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        info = await self.get_swap_instruments_info(symbol)
        return int(round((log(1 / float(info['tickSize']), 10)), 0))

    # 获取下单数量精度
    async def get_swap_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        info = await self.get_swap_instruments_info(symbol)
        return max(int(round((log(1 / float(info['stepSize']), 10)), 0)), 0)

    # 获取最小下单数量
    async def get_swap_min_amount(self, symbol):
        """
        最小下单金额10u
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _price = await self.get_swap_buy1(symbol)
            _amount_precision = await self.get_swap_order_amount_tick_size(symbol)
            _info = await self.exchange.markets.get_perpetual_markets()
            for key, value in _info['markets'].items():
                if symbol.upper() + '-USD' == key:
                    step_size = float(value['stepSize'])
                    break
            return max(ceil(10 / _price * 10 ** _amount_precision) / 10 ** _amount_precision, step_size)
        except:
            print(format_exc())
            return None

    # 获取永续合约交易对
    async def get_swap_instruments_symbols(self, base_symbol='usdc'):
        """
        添加usdt是为了后续和其他交易所做比对，实际应该为usdc合约
        :return:
        """
        try:
            info = await self.exchange.markets.get_perpetual_markets()
            _symbols_list = [i.lower().replace('-usd', base_symbol)
                             for i in info['markets'].keys()]
            return _symbols_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdc)
    async def get_swap_account_single_asset(self, symbol=None):
        try:
            subaccounts_response = await self.exchange.account.get_subaccounts(self.address)
            subaccounts = float(subaccounts_response['subaccounts'][0]['freeCollateral'])
            return subaccounts
        except:
            print(format_exc())
            return None

    # 获取swap账户持仓信息
    async def get_swap_position(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        {
            positions: [
                {
                market: "SUI-USD",
                status: "OPEN",
                side: "SHORT",
                size: "-120000",
                maxSize: "-1000",
                entryPrice: "3.1082181652892561983",
                exitPrice: "3.171754",
                realizedPnl: "1586.****************",
                unrealizedPnl: "-7392.***************",
                createdAt: "2024-11-10T12:15:32.029Z",
                createdAtHeight: "********",
                closedAt: None,
                sumOpen: "121000",
                sumClose: "1000",
                netFunding: "1650.4092",
                subaccountNumber: 0
                }
            ]
            }
        """
        try:
            info = await self.exchange.account.get_subaccount_perpetual_positions(self.address, self.subaccount_number, 'OPEN')
            positions = info['positions']
            if symbol:
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if i['side'] == 'LONG' else 'sell',
                        'amount': abs(float(i['size'])),
                        'price': i['entryPrice'],
                        'liquidation_price': '',
                        'net_funding': i['netFunding']
                    } for i in positions if i['market'] == symbol.upper() + '-USD'
                ]
            else:
                return [
                    {
                        'symbol': i['market'].lower().replace('-usd', ''),
                        'direction': 'buy' if i['side'] == 'LONG' else 'sell',
                        'amount': abs(float(i['size'])),
                        'price': i['entryPrice'],
                        'liquidation_price': '',
                        'net_funding': i['netFunding']
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    async def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        """
        try:
            open_order = await self.exchange.account.get_subaccount_orders(self.address, self.subaccount_number, status='OPEN')
            if symbol:
                return [
                    {
                        'order_id': i['id'],
                        'symbol': symbol,
                        'direction': 'buy' if i['side'] == 'BUY' else 'sell',
                        'order_type': i['type'].lower(),
                        'amount': i['size'],
                        'price': i['price'],
                        'average_price': '',
                        'remain_amount': float(i['size']) - float(i['totalFilled']),
                    } for i in open_order if i['ticker'] == symbol.upper() + '-USD'
                ]
            else:
                return [
                    {
                        'order_id': i['id'],
                        'symbol': i['ticker'].lower().replace('-usd', ''),
                        'direction': 'buy' if i['side'] == 'BUY' else 'sell',
                        'order_type': i['type'].lower(),
                        'amount': i['size'],
                        'price': i['price'],
                        'average_price': '',
                        'remain_amount': float(i['size']) - float(i['totalFilled']),
                    } for i in open_order
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    async def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            open_order = await self.get_swap_open_order(symbol)
            return open_order[0]['order_id']
        except:
            print(format_exc())
            return None

    # TODO: 下单
    async def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:  暂时无法使用，平仓reduce_only参数有问题，下反向limit订单即可
        :return:
        """
        try:
            market = Market(
                (await self.exchange.markets.get_perpetual_markets(symbol.upper() + '-USD'))["markets"][symbol.upper() + '-USD']
            )
            order_id = market.order_id(
                self.address, 0, random.randint(0, MAX_CLIENT_ID), OrderFlags.SHORT_TERM
            )
            if order_type == 'limit':
                if not close_position:
                    order_info = await self.node.place_order(
                        self.wallet,
                        market.order(
                            order_id,
                            OrderType.LIMIT,
                            Order.Side.SIDE_BUY if direction == 'buy' else Order.Side.SIDE_SELL,
                            size=float(amount),
                            price=float(price),
                            time_in_force=Order.TimeInForce.TIME_IN_FORCE_UNSPECIFIED,
                            reduce_only=False,
                            good_til_block_time=since_now(hours=999),
                        ),
                    )
                else:
                    # TODO：平仓reduce_only参数有问题，暂时无法使用
                    order_info = await self.node.place_order(
                        self.wallet,
                        market.order(
                            order_id,
                            OrderType.LIMIT,
                            Order.Side.SIDE_BUY if direction == 'buy' else Order.Side.SIDE_SELL,
                            size=float(amount),
                            price=float(price),
                            time_in_force=Order.TimeInForce.TIME_IN_FORCE_UNSPECIFIED,
                            reduce_only=True,
                            good_til_block_time=since_now(hours=999),
                        ),
                    )
            # 无市价单
            elif order_type == 'market':
                pass
            else:
                return None
            # client_id = str(order_id.client_id)
            return order_info
            # await asyncio.sleep(2)  # 等待indexer生成订单以获取订单id
            # orders = await self.exchange.account.get_subaccount_orders(self.address, self.subaccount_number, ticker=symbol.upper() + '-USD')
            # for i in orders:
            #     if i['clientId'] == client_id:
            #         _id = i['id']
            #         break
            # return {
            #     'exchange': self.exchange_name,
            #     'order_id': _id,
            #     'symbol': symbol,
            #     'direction': direction,
            #     'amount': amount,
            #     'price': price,
            #     'order_type': order_type,
            # }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    async def get_swap_order_info(self, symbol=None, order_id=None):
        """
        :param symbol:
        :param order_id:
        :return:
        {
            id: "c95b3f7a-b7c5-5812-9220-953e907c3ad4",
            subaccountId: "4eeea5bc-1cf9-545a-b8a8-25b3432bf97d",
            clientId: "**********",
            clobPairId: "10",
            side: "BUY",
            size: "100",
            totalFilled: "0",
            price: "0.25",
            type: "LIMIT",
            status: "OPEN",
            timeInForce: "GTT",
            reduceOnly: False,
            orderFlags: "64",
            goodTilBlockTime: "2024-12-09T10:07:47.000Z",
            createdAtHeight: "********",
            clientMetadata: "0",
            updatedAt: "2024-11-11T10:07:46.495Z",
            updatedAtHeight: "********",
            postOnly: False,
            ticker: "DOGE-USD",
            subaccountNumber: 0
            }
        """
        try:
            order_info = await self.exchange.account.get_order(order_id)
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol,
                'direction': 'buy' if order_info['side'] == 'BUY' else 'sell',
                'order_type': order_info['type'].lower(),
                'amount': order_info['size'],
                'price': order_info['price'],
                'average_price': '',    # 暂时无成交价信息
                'remain_amount': float(order_info['size']) - float(order_info['totalFilled']),
                'fee': '',
            }
        except:
            print(format_exc())
            return None

    # TODO: 撤销订单(报错)
    async def cancel_swap_order(self, symbol, order_id):
        try:
            order_info = await self.node.cancel_order(
                self.wallet,
                order_id,
                good_til_block_time=since_now(seconds=120),
            )
            print(order_info)
            exit()
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info['response']['data']['statuses'][0] == 'success' else 'failed'
            }
        except:
            print(format_exc())
            return None
    # endregion


# async def main():
#     adapter = await DydxV4Adapter.create()
#     result = await adapter.place_swap_order('doge', 'buy', 100, 0.35, order_type='limit')
#     # result = await adapter.cancel_swap_order('doge', '694d93d9-6cc9-5937-8d0f-a24f324b79d8')
#     # result = await adapter.get_swap_order_info('doge', '694d93d9-6cc9-5937-8d0f-a24f324b79d8')
#     print(result)

# if __name__ == "__main__":
#     asyncio.run(main())
