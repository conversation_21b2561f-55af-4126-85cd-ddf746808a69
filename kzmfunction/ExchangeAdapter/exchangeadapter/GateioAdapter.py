# region =============================================备注说明
"""
备注说明：
    官方sdk:
    https://github.com/gateio/gateapi-python
    pip install --user gate-api
"""
# endregion =========================================备注说明


# region =============================================import
from .ExchangeAdapter import ExchangeAdapter
from traceback import format_exc
import sys
import os
from math import ceil, log
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ..api_config import gateio_api_config
from gate_api import ApiClient, Configuration, Order, SpotApi, FuturesApi, FuturesOrder, Transfer, WalletApi
# endregion =============================================import


# region =============================================class
class GateioAdapter(ExchangeAdapter):
    def __init__(self, account='<EMAIL>'):
        self._api_config = gateio_api_config.api_config
        self.account = account
        self.exchange_name = 'gateio'
        # 创建交易所
        if account:
            spot_config = Configuration(key=self._api_config[account]['api_key'], secret=self._api_config[account]['secret_key'])
            self.spot_exchange = SpotApi(ApiClient(spot_config))
            swap_config = Configuration(key=self._api_config[account]['api_key'], secret=self._api_config[account]['secret_key'])
            self.swap_exchange = FuturesApi(ApiClient(swap_config))
        else:
            spot_config = Configuration(key=self._api_config['查询']['api_key'], secret=self._api_config['查询']['secret_key'])
            self.spot_exchange = SpotApi(ApiClient(spot_config))
            swap_config = Configuration(key=self._api_config['查询']['api_key'], secret=self._api_config['查询']['secret_key'])
            self.swap_exchange = FuturesApi(ApiClient(swap_config))

    # region =============================================spot
    # 获取spot最新成交价格
    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        try:
            return float(self.spot_exchange.list_tickers_with_http_info(currency_pair=symbol.upper() + '_USDT')[0][0].last)
        except:
            print(format_exc())
            return None

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
    
        """
        try:
            return float(self.spot_exchange.list_order_book_with_http_info(currency_pair=symbol.upper() + '_USDT')[0].bids[0][0])
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:
        """
        try:
            return float(self.spot_exchange.list_order_book_with_http_info(currency_pair=symbol.upper() + '_USDT')[0].asks[0][0])
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
        """
        try:
            raw_orderbook = self.spot_exchange.list_order_book_with_http_info(currency_pair=symbol.upper() + '_USDT', limit=1)
            return {
                'bid': [raw_orderbook[0].bids[0][0], raw_orderbook[0].bids[0][1]],
                'ask': [raw_orderbook[0].asks[0][0], raw_orderbook[0].asks[0][1]]
            }
        except:
            print(format_exc())
            return None

    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              61102.74,
              2.49015
            ],
            [
              61102.73,
              0.01893
            ],
            ...
          ],
          "asks": [
            [
              61102.75,
              0.01583
            ],
            [
              61102.79,
              9e-05
            ],
            ...
          ]
        }
        """
        try:
            raw_orderbook = self.spot_exchange.list_order_book_with_http_info(currency_pair=symbol.upper() + '_USDT', limit=limit)
            return {
                'symbol': symbol,
                'bids': raw_orderbook[0].bids,
                'asks': raw_orderbook[0].asks
            }
        except:
            print(format_exc())
            return None

    # TODO：
    def get_spot_kline(self):
        pass

    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            id: "ZK_USDT",
            base: "ZK",
            quote: "USDT",
            fee: "0.2",
            min_base_amount: "0.01",
            min_quote_amount: "3",
            max_quote_amount: "5000000",
            amount_precision: "2",
            precision: "5",
            trade_status: "tradable",
            sell_start: "1596702900",
            buy_start: "1718611200"
            }
        """
        try:
            return self.spot_exchange.get_currency_pair_with_http_info(currency_pair=symbol.upper() + '_USDT')[0].to_dict()
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(self.spot_exchange.get_currency_pair_with_http_info(currency_pair=symbol.upper() + '_USDT')[0].to_dict()['precision'])
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(self.spot_exchange.get_currency_pair_with_http_info(currency_pair=symbol.upper() + '_USDT')[0].to_dict()['amount_precision'])
        except:
            print(format_exc())
            return None

    # 获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        """
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(self.spot_exchange.get_currency_pair_with_http_info(currency_pair=(symbol + '_' + base_symbol).upper())[0].to_dict()['precision'])
        except:
            print(format_exc())
            return None

    # 获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        """
        获取下单数量精度
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(self.spot_exchange.get_currency_pair_with_http_info(currency_pair=(symbol + '_' + base_symbol).upper())[0].to_dict()['amount_precision'])
        except:
            print(format_exc())
            return None

    # FIXME:获取最小下单数量(gateio获取的最小下单数量有问题,实际以1u折算成数量为准)
    def get_spot_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.spot_exchange.get_currency_pair_with_http_info(currency_pair=symbol.upper() + '_USDT')[0].to_dict()
            _symbol_price = self.get_spot_buy1(symbol)
            _min_amount = float(_info['min_base_amount'])
            _amount_precision = int(_info['amount_precision']) * 10 ** _info['precision']
            _min_amount2 = ceil(1.05 / _symbol_price * 10 ** _amount_precision) / 10 ** _amount_precision
            return max(_min_amount, _min_amount2)
        except:
            print(format_exc())
            return None
        
    # 获取现货币对
    def get_spot_instruments_symbols(self, base_symbol='usdt'):
        """`

        :return:
        """
        try:
            symbol_list = []
            _info = self.spot_exchange.list_currency_pairs_with_http_info()[0]
            symbol_list.extend([(i.base + base_symbol).lower() for i in _info if i.quote == base_symbol.upper() and i.trade_status == 'tradable'])
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol):
        """
        :param symbol:
        :return:
        { available: "56983.***********", currency: "USDT", locked: "0" }
        """
        try:
            return float(self.spot_exchange.list_spot_accounts_with_http_info(currency=symbol.upper())[0][0].available)
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
            [
                { available: "28396.652462", currency: "BLUE", locked: "0" },
                { available: "141565", currency: "LAUNCHCOIN", locked: "0" },
                { available: "10764", currency: "PROMPT", locked: "0" },
                { available: "28394.53", currency: "AZUR", locked: "0" },
                { available: "0", currency: "NEIROETH", locked: "0" },
                { available: "56983.***********", currency: "USDT", locked: "0" },
                { available: "3.************", currency: "GT", locked: "0" },
                { available: "0", currency: "ZORA", locked: "0" },
                { available: "0", currency: "ZEREBRO", locked: "0" },
                { available: "0", currency: "ZKF", locked: "0" },
                { available: "0.********", currency: "PFVS", locked: "0" },
                { available: "0", currency: "GRIFFAIN", locked: "0" },
                { available: "0", currency: "ETH", locked: "0" },
                { available: "0", currency: "MUBARAK", locked: "0" },
                { available: "0", currency: "FARTCOIN", locked: "0" },
                { available: "0", currency: "USDC", locked: "0" },
                { available: "0", currency: "SWELL", locked: "0" },
                { available: "0.************", currency: "POINT", locked: "0" }
            ]
        """
        try:
            balance = self.spot_exchange.list_spot_accounts_with_http_info()[0]
            return {i.currency.lower(): i.available for i in balance if float(i.available) > 0}
        except:
            print(format_exc())
            return None

    # 获取spot挂单订单信息
    def get_spot_open_order(self, symbol):
        """
        :param symbol:
        :return:
        [
          {
            "id": "************",
            "clientOrderId": "3",
            "timestamp": *************,
            "datetime": "2024-08-18T12:49:48.093Z",
            "lastTradeTimestamp": *************,
            "status": "open",
            "symbol": "ETH/USDT",
            "type": "limit",
            "timeInForce": "GTC",
            "postOnly": false,
            "reduceOnly": "None",
            "side": "buy",
            "price": 2500.0,
            "stopPrice": "None",
            "triggerPrice": "None",
            "average": "None",
            "amount": 0.01,
            "cost": 0.0,
            "filled": 0.0,
            "remaining": 0.01,
            "fee": "None",
            "fees": [],
            "trades": [],
            "info": {
              "id": "************",
              "text": "3",
              "amend_text": "-",
              "create_time": "**********",
              "update_time": "**********",
              "create_time_ms": "*************",
              "update_time_ms": "*************",
              "status": "open",
              "currency_pair": "ETH_USDT",
              "type": "limit",
              "account": "spot",
              "side": "buy",
              "amount": "0.01",
              "price": "2500",
              "time_in_force": "gtc",
              "iceberg": "0",
              "left": "0.01",
              "filled_amount": "0",
              "fill_price": "0",
              "filled_total": "0",
              "fee": "0",
              "fee_currency": "ETH",
              "point_fee": "0",
              "gt_fee": "0",
              "gt_maker_fee": "0",
              "gt_taker_fee": "0",
              "gt_discount": false,
              "rebated_fee": "0",
              "rebated_fee_currency": "USDT",
              "finish_as": "open"
            },
            "lastUpdateTimestamp": "None",
            "takeProfitPrice": "None",
            "stopLossPrice": "None"
          }
        ]
        """
        try:
            open_orders = self.spot_exchange.list_all_open_orders_with_http_info()[0]
            return [{
                'order_id': order.orders[0].id,
                'symbol': symbol,
                'direction': order.orders[0].side,
                'amount': order.orders[0].amount,
                'price': order.orders[0].price,
                'order_type': order.orders[0].type,
                'average_price': '',
                'remain_amount': order.orders[0].left,
            } for order in open_orders if order.currency_pair == symbol.upper() + '_USDT']
        except:
            print(format_exc())
            return None

    # 下单 (gateio暂时没有支持传入下单币数量类型的市价单)
    def place_spot_order(self, symbol, direction, amount=None, price=None, order_type='limit', quote_order_qty=None):
        """
        :param symbol:
        :param direction:
        :param amount:
        amount: 交易数量
            type为limit时，指交易货币，即需要交易的货币，如BTC_USDT中指BTC。
            type为market时，根据买卖不同指代不同

            side : buy 指代计价货币，BTC_USDT中指USDT
            side : sell 指代交易货币，BTC_USDT中指BTC
        :param price:
        :param order_type:
        :param params:
        :return:
        {'info':
            {'symbol': 'BNBUSDT',
            'orderId': 1354887854,
            'orderListId': -1,
            'clientOrderId': 'VNiGuPb9EMPwjKQQBCqVAz',
            'transactTime': 1612083702732,
            'price': '50.00000000',
            'origQty': '0.50000000',
            'executedQty': '0.00000000',
            'cummulativeQuoteQty': '0.00000000',
            'status': 'NEW',
            'timeInForce': 'GTC',
            'type': 'LIMIT',
            'side': 'SELL'
            },
        'id': '1354887854',
        'timestamp': 1612083702732,
        'datetime': '2021-01-31T09:01:42.732Z',
        'lastTradeTimestamp': None,
        'symbol': 'BNB/USDT',
        'type': 'limit',
        'side': 'sell',
        'price': 50.0,
        'amount': 0.5,
        'cost': 0.0,
        'average': None,
        'filled': 0.0,
        'remaining': 0.5,
        'status': 'open',
        'fee': None,
        'trades': None
        }
        """
        try:
            if order_type == 'limit':
                order_info = self.spot_exchange.create_order_with_http_info(
                    Order(currency_pair=symbol.upper() + '_USDT', side=direction, type='limit', price=price, amount=amount))[0]
            elif order_type == 'market':
                if quote_order_qty:
                    order_info = self.spot_exchange.create_order_with_http_info(
                        Order(currency_pair=symbol.upper() + '_USDT', side=direction, type='market', amount=quote_order_qty, time_in_force='ioc'))[0]
                else:
                    print('当前交易所不支持传入具体下单币数量类型的市价单')
                    return None
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info.id,
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info.price,
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # TODO：下单 spot下单(适用于交叉币种,比如btc/eth, gateio暂时没有市价单)
    def place_spot_order2(self, symbol, base_symbol, direction, amount, price='', order_type='limit'):
        try:
            if order_type == 'limit':
                order_info = self.spot_exchange.create_order(symbol=symbol.upper(
                ) + '/' + base_symbol.upper(), side=direction, type='limit', price=price, amount=amount)
            elif order_type == 'market':
                # (gateio暂时没有市价单)
                return None
            else:
                return None
            return {
                'exchange': self.spot_exchange_name,
                'order_id': order_info['id'],
                'symbol': symbol + '/' + base_symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info['price'],
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取spot订单信息
    def get_spot_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
            id: "************",
            clientOrderId: "apiv4",
            timestamp: *************,
            datetime: "2024-09-19T11:11:10.259Z",
            lastTradeTimestamp: *************,
            status: "closed",
            symbol: "POPCAT/USDT",
            type: "limit",
            timeInForce: "GTC",
            postOnly: False,
            reduceOnly: None,
            side: "sell",
            price: 0.8763,
            stopPrice: None,
            triggerPrice: None,
            average: 0.8844,
            amount: 917.0,
            cost: 810.9948,
            filled: 917.0,
            remaining: 0.0,
            fee: None,
            fees: [
                { currency: "GT", cost: 0.0 },
                { currency: "USDT", cost: 0.0 },
                { currency: "POPCAT", cost: 0.0 }
            ],
            trades: [],
            info: {
                id: "************",
                text: "apiv4",
                amend_text: "-",
                create_time: "**********",
                update_time: "**********",
                create_time_ms: "*************",
                update_time_ms: "*************",
                status: "closed",
                currency_pair: "POPCAT_USDT",
                type: "limit",
                account: "unified",
                side: "sell",
                amount: "917.0",
                price: "0.8763",
                time_in_force: "gtc",
                iceberg: "0.0",
                left: "0.0",
                filled_amount: "917.0",
                fill_price: "810.9948",
                filled_total: "810.9948",
                avg_deal_price: "0.8844",
                fee: "0.0",
                fee_currency: "USDT",
                point_fee: "1.6219896",
                gt_fee: "0.0",
                gt_maker_fee: "0.0",
                gt_taker_fee: "0.0",
                gt_discount: False,
                rebated_fee: "0.0",
                rebated_fee_currency: "POPCAT",
                finish_as: "filled"
            },
            lastUpdateTimestamp: None,
            takeProfitPrice: None,
            stopLossPrice: None
            }
        """
        try:
            order_info = self.spot_exchange.get_order_with_http_info(currency_pair=symbol.upper() + '_USDT', order_id=order_id)[0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_info.id,
                'symbol': symbol,
                'direction': order_info.side,
                'order_type': order_info.type,
                'amount': order_info.amount,
                'price': order_info.price,
                'average_price': order_info.avg_deal_price,
                'remain_amount': order_info.left,
                'fee': order_info.point_fee      # point_fee 抵扣手续费
            }
        except:
            print(format_exc())
            return None

    # TODO：获取spot订单信息(适用于交叉币种,比如btc/eth)
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        try:
            order_info = self.spot_exchange.get_order_with_http_info(
                currency_pair=symbol.upper() + '/' + base_symbol.upper(), order_id=order_id)[0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_info.id,
                'symbol': symbol + '/' + base_symbol,
                'direction': order_info.side,
                'order_type': order_info.type,
                'amount': order_info.amount,
                'price': order_info.price,
                'average_price': order_info.avg_deal_price,
                'remain_amount': order_info.left,
                'fee': order_info.point_fee      # point_fee 抵扣手续费
            }
        except:
            print(format_exc())
            return None

    # 取消spot订单
    def cancel_spot_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {
            'account': 'unified',
            'amend_text': '-',
            'amount': '0.0001',
            'auto_borrow': None,
            'auto_repay': None,
            'avg_deal_price': None,
            'create_time': '**********',
            'create_time_ms': **********762,
            'currency_pair': 'BTC_USDT',
            'fee': '0',
            'fee_currency': 'BTC',
            'fill_price': '0',
            'filled_total': '0',
            'finish_as': 'cancelled',
            'gt_discount': True,
            'gt_fee': '0',
            'gt_maker_fee': '0.00081',
            'gt_taker_fee': '0.00085',
            'iceberg': '0',
            'id': '************',
            'left': '0.0001',
            'point_fee': '0',
            'price': '102000',
            'rebated_fee': '0',
            'rebated_fee_currency': 'USDT',
            'side': 'buy',
            'status': 'cancelled',
            'stp_act': None,
            'stp_id': None,
            'text': 'web',
            'time_in_force': 'gtc',
            'type': 'limit',
            'update_time': '1748574781',
            'update_time_ms': 1748574781169
        }
        """
        try:
            order_info = self.spot_exchange.cancel_order_with_http_info(currency_pair=symbol.upper() + '_USDT', order_id=order_id)[0]
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info.status == 'cancelled' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO 交割合约
    # region =======================================future
    # 获取合约当期instrument_id
    def get_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        """
        根据输入的symbol获取当期周期(当周/次周/季度/次季度)的合约id
        对于季度合约:
            如果不指定contract_type,则返回当周,次周,季度,次季度 四个instrument_id组成的list
            如果指定contract_type,则返回单个instrument_id的str
        @param symbol:  BTC
        @param margin_type:  币本位coin or U本位usdt
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:
        """
        instrument_id = self.spot_exchange.get_products()
        margin_type2 = {'coin': 'USD', 'usdt': 'USDT'}.get(margin_type)
        if contract_type:
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['alias'] == contract_type and i['quote_currency'] == margin_type2:
                    return i['instrument_id']
        else:
            instrument_id_list = []
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['quote_currency'] == margin_type2:
                    instrument_id_list.append(i['instrument_id'])
            return instrument_id_list

    # 获取future盘口买1价
    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的买1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])

    # 获取future盘口卖1价
    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的卖1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])

    # # 获取future-K线
    # def get_future_kline(self, symbol, margin_type='usdt', contract_type='quarter', period='15min', start='', end=''):
    #     """
    #     获取future-K线
    #     @param symbol:  btc
    #     @param margin_type: 币本位(coin)或U本位(usdt)
    #     @param contract_type:   this_week, next_week, quarter, bi_quarter
    #     @param period:  时间周期, min、hour、day、week  okex输入参数以秒为单位，默认值60。如[60/180/300/900/1800/3600/7200/14400/21600/43200/86400/604800]
    #     @param start:  str
    #     @param end:   str
    #     @return:    future：# 300根K线
    #     """
    #     # 获取K线周期，转为秒
    #     if 'min' in period:
    #         granularity = str(get_number(period) * 60).split('.')[0]
    #     elif 'hour' in period:
    #         granularity = str(get_number(period) * 3600).split('.')[0]
    #     elif 'day' in period:
    #         granularity = str(get_number(period) * 86400).split('.')[0]
    #     elif 'week' in period:
    #         granularity = str(get_number(period) * 604800).split('.')[0]
    #     else:
    #         granularity = '900'  # 默认15min
    #
    #     # 获取起始时间
    #     if start and end:
    #         start = (pd.to_datetime(start) - timedelta(hours=8)).isoformat("T") + "Z"
    #         end = (pd.to_datetime(end) - timedelta(hours=8)).isoformat("T") + "Z"
    #
    #     # =======================================交割合约K线
    #     instrument_id = self.get_instrument_id(symbol, margin_type, contract_type)  # 获取合约id
    #     if margin_type == 'coin':  # 币本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     elif margin_type == 'usdt':  # U本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     else:
    #         kline = pd.DataFrame()
    #     # 转为Dataframe，转为北京时区
    #     kline = pd.DataFrame(kline, columns=['candle_begin_time', 'open', 'high', 'low', 'close', 'cont', 'volume'])
    #     kline['candle_begin_time'] = pd.to_datetime(kline['candle_begin_time']) + timedelta(hours=8)
    #
    #     return kline

    # 获取future账户余额
    def get_future_account(self, symbol, margin_type='usdt'):
        """
        返回future账户的余额（币或USDT）
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        # ======================================= 交割合约:
            币本位：
                {
                  'equity': '105.********',
                  'margin': '106.********',
                  'realized_pnl': '0',
                  'unrealized_pnl': '1.********',
                  'margin_ratio': '0.********',     保证金率
                  'margin_mode': 'crossed',
                  'total_avail_balance': '104.********',
                  'margin_frozen': '106.********',
                  'margin_for_unfilled': '0',
                  'liqui_mode': 'tier',
                  'maint_margin_ratio': '0.02',
                  'liqui_fee_rate': '0.00035',
                  'can_withdraw': '0',  可划转数量
                  'underlying': 'BSV-USD',
                  'currency': 'BSV'
                }
            U本位：
                {
                  'total_avail_balance': '66.********',
                  'contracts': None,
                  'equity': '66.********',
                  'margin_mode': 'fixed',
                  'auto_margin': '0',
                  'liqui_mode': 'tier',
                  'can_withdraw': '66.********',
                  'currency': 'USDT'
                }
        """
        if margin_type == 'coin':  # 币本位
            return self.exchange.get_coin_account(symbol.upper() + '-USD')
        elif margin_type == 'usdt':  # U本位
            return self.exchange.get_coin_account(symbol.upper() + '-USDT')

    # 获取future持仓信息
    def get_future_position(self, symbol, margin_type='usdt'):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:    返回持仓列表
        交割合约：
            币本位：
                [{
                  'long_qty': '4145',
                  'long_avail_qty': '4145',
                  'long_avg_cost': '195.********',
                  'long_settlement_price': '194.08',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_avg_cost': '195.********',
                  'short_settlement_price': '195.********',
                  'liquidation_price': '132.61',
                  'instrument_id': 'BSV-USD-200626',
                  'leverage': '2',
                  'created_at': '2020-04-02T23:37:37.220Z',
                  'updated_at': '2020-04-18T08:00:10.913Z',
                  'margin_mode': 'crossed',
                  'short_margin': '0.0',
                  'short_pnl': '0.0',
                  'short_pnl_ratio': '-0.03896512',
                  'short_unrealised_pnl': '0.0',
                  'long_margin': '104.08296505',
                  'long_pnl': '3.87219199',
                  'long_pnl_ratio': '0.03652354',
                  'long_unrealised_pnl': '5.40579291',
                  'long_settled_pnl': '-1.53360092',
                  'short_settled_pnl': '0',
                  'last': '199.14'
                }]
            U本位：
                [{
                  'long_qty': '0',
                  'long_avail_qty': '0',
                  'long_margin': '0',
                  'long_liqui_price': '0',
                  'long_pnl_ratio': '0',
                  'long_avg_cost': '0',
                  'long_settlement_price': '0',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_margin': '0',
                  'short_liqui_price': '0',
                  'short_pnl_ratio': '0',
                  'short_avg_cost': '0',
                  'short_settlement_price': '0',
                  'instrument_id': 'BSV-USDT-200626',
                  'long_leverage': '10',
                  'short_leverage': '10',
                  'created_at': '1970-01-01T00:00:00.000Z',
                  'updated_at': '1970-01-01T00:00:00.000Z',
                  'margin_mode': 'fixed',
                  'short_margin_ratio': '0',
                  'short_maint_margin_ratio': '0',
                  'short_pnl': '0',
                  'short_unrealised_pnl': '0',
                  'long_margin_ratio': '0',
                  'long_maint_margin_ratio': '0',
                  'long_pnl': '0',
                  'long_unrealised_pnl': '0',
                  'long_settled_pnl': '0',
                  'short_settled_pnl': '0',
                  'last': '199.53'
                }]
        """
        # =======================================交割合约持仓信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取交割合约id

        for i in instrument_id:
            temp_position = self.exchange.get_specific_position(i)['holding']

            if temp_position[0]['long_qty'] != '0' or temp_position[0]['short_qty'] != '0':
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单信息
    def get_future_open_order(self, symbol, margin_type='usdt'):
        """
        返回future账户的挂单信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        交割合约：
            币本位：
            [{'instrument_id': 'BSV-USD-200501',
                  'size': '1',
                  'timestamp': '2020-04-19T07:00:04.943Z',
                  'filled_qty': '0',    # 成交数量
                  'fee': '0',
                  'order_id': '4754217692783617',
                  'price': '190',   委托价格
                  'price_avg': '0',     成交均价
                  'status': '0',
                  'state': '0',     -2：失败-1：撤单成功0：等待成交1：部分成交2：完全成交3：下单中4：撤单中
                  'type': '1',      1:开多2:开空3:平多4:平空
                  'contract_val': '10',     合约面值
                  'leverage': '2',
                  'client_oid': '',
                  'pnl': '0',   收益
                  'order_type': '0'
            }]
            U本位：
                [{'instrument_id': 'BSV-USDT-200501',
                   'size': '1',
                   'timestamp': '2020-04-19T07:15:15.196Z',
                   'filled_qty': '0',
                   'fee': '0',
                   'order_id': '4754277346794497',
                   'price': '190',
                   'price_avg': '0',
                   'status': '0',
                   'state': '0',
                   'type': '1',     1:开多2:开空3:平多4:平空
                   'contract_val': '1',
                   'leverage': '10',
                   'client_oid': '',
                   'pnl': '0',
                   'order_type': '0'
                }]
        """
        # =======================================交割合约挂单信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取合约id

        for i in instrument_id:
            temp_position = self.exchange.get_order_list(i, state='6')[
                'order_info']
            if temp_position:
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单订单号
    def get_future_open_order_id(self, symbol, margin_type):
        """
        返回future账户的挂单订单号
        :param symbol:
        :param margin_type:
        :return:
        """
        # return self.get_future_open_order(symbol, margin_type)
        return self.get_future_open_order(symbol, margin_type)[0]['order_id']

    # TODO 根据订单号获取future订单详细信息
    def get_future_order_info(self, order_id, instrument_id=''):
        """
        @param order_id:
        @param instrument_id:
        @return:
        交割合约订单信息：
            {
              'instrument_id': 'BSV-USD-200626',
              'size': '1',
              'timestamp': '2020-04-22T14:46:12.585Z',
              'filled_qty': '0',    成交数量
              'fee': '0',
              'order_id': '4773037511302145',
              'price': '180',
              'price_avg': '0',
              'status': '0',
              'state': '0',     -2:失败 -1:撤单成功 0:等待成交 1:部分成交 2:完全成交 3:下单中 4:撤单中
              'type': '1',      1:开多 2:开空 3:平多 4:平空
              'contract_val': '10',     合约面值
              'leverage': '1',
              'client_oid': '',
              'pnl': '0',   收益
              'order_type': '0'
            }
        """

        return self.exchange.get_order_info(instrument_id, order_id)

    # 获取future可平仓数量
    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        """
        获取future可平仓数量,只针对持有单一合约有效，若有平仓的挂单，则不含这部分持仓
        @param symbol: btc
        @param margin_type: coin or usdt
        @return:
        """
        order_info = self.get_future_position(symbol, margin_type)[0]
        if order_info['long_avail_qty'] != '0':
            return float(order_info['long_avail_qty'])
        elif order_info['short_avail_qty'] != '0':
            return float(order_info['short_avail_qty'])

    # future下单
    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        """

        @param symbol: btc
        @param amount:
        @param direction: buy,sell,close_buy,close_sell
        @param price:
        @param order_type: limit market
        @param margin_type: coin,usdt
        @param contract_type: this_week, next_week, quarter, bi_quarter
        @return:
        参数名	参数类型	描述
            order_id	String	订单ID，下单失败时，此字段值为-1
            client_oid	String	由您设置的订单ID来识别您的订单
            error_code	String	错误码，下单成功时为0，下单失败时会显示相应错误码
            error_message	String	错误信息，下单成功时为空，下单失败时会显示错误信息
            result	Boolean	调用接口返回结果
        """
        type_dict = {'buy': '1', 'sell': '2',
                     'close_buy': '3', 'close_sell': '4'}
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id

        if order_type == 'limit':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=str(price), size=str(amount))
        elif order_type == 'market':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=price, order_type='4', size=str(amount))
        else:
            order_info = None
        return order_info

    # future 撤单
    def cancel_future_order(self, order_id, instrument_id):
        """

        @param order_id: str 订单号
        @param instrument_id: str
        @return:
        """
        cancel_order_info = self.exchange.revoke_order(
            instrument_id, order_id=order_id)

        return cancel_order_info

    # endregion


    # region =======================================usdt swap
    # TODO：获取最新价格
    def get_swap_latest_price(self, symbol):
        try:
            return float(self.swap_exchange.list_tickers_with_http_info(symbol.upper() + '_USDT'))
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.swap_exchange.list_futures_order_book_with_http_info(contract=symbol.upper() + '_USDT', settle='usdt', limit=1)[0].bids[0].p)
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.swap_exchange.list_futures_order_book_with_http_info(contract=symbol.upper() + '_USDT', settle='usdt', limit=1)[0].asks[0].p)
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60896.40', '4.079'], 'ask': ['60896.50', '5.771']}
        """
        try:
            raw_orderbook = self.swap_exchange.list_futures_order_book_with_http_info(contract=symbol.upper() + '_USDT', settle='usdt', limit=1)
            return {
                'bid': [raw_orderbook[0].bids[0].p, raw_orderbook[0].bids[0].s],
                'ask': [raw_orderbook[0].asks[0].p, raw_orderbook[0].asks[0].s]
            }
        except:
            print(format_exc())
            return None

    # 获取orderbook
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              "61069.90",
              "2.459"
            ],
            [
              "61069.70",
              "0.270"
            ],
            ...
          ],
          "asks": [
            [
              "61070.00",
              "4.226"
            ],
            [
              "61070.30",
              "0.035"
            ],
            ...
          ]
        }
        """
        try:
            raw_orderbook = self.swap_exchange.list_futures_order_book_with_http_info(contract=symbol.upper() + '_USDT', settle='usdt', limit=limit)
            bids, asks = [], []
            for i in raw_orderbook[0].bids:
                bids.append([i.p, i.s])
            for i in raw_orderbook[0].asks:
                asks.append([i.p, i.s])
            return {
                'symbol': symbol,
                'bids': bids,
                'asks': asks
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float(self.swap_exchange.get_futures_contract_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0].funding_rate)
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在
            {
              "symbol": "btc",
              "funding_rate": [
                "0.00004846",
                "-0.00003942",
                "-0.00001379",
                "-0.00001949",
                "0.00004049"
              ],
              "funding_time": [
                "1723219200000",
                "1723248000000",
                "1723276800000",
                "1723305600000",
                "1723334400000"
              ]
            }
        """
        try:
            funding_rates = self.swap_exchange.list_futures_funding_rate_history_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT', limit=limit)[0]
            return {
                'symbol': symbol,
                'funding_rate': [i.r for i in funding_rates][::-1],
                'funding_time': [i.t for i in funding_rates][::-1]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
            u本位：
            {'symbol': 'BNBUSDT',
            'sumOpenInterest': '953893.97000000',   持仓的bnb数量
            'sumOpenInterestValue': '350101980.44528000', 持仓的bnb价值(usdt)
            'timestamp': 1617795600000
            }
        """
        try:
            return float(self.swap_exchange.list_contract_stats_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0][0].open_interest_usd)
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            'config_change_time': 1746773287.0,
            'create_time': 1604880000.0,
            'enable_bonus': True,
            'enable_credit': True,
            'funding_cap_ratio': '0.75',
            'funding_interval': 28800,
            'funding_next_apply': 1748592000.0,
            'funding_rate': '-0.000157',
            'in_delisting': False,
            'index_price': '165.378',
            'last_price': '165.27',
            'leverage_max': '100',
            'leverage_min': '1',
            'maintenance_rate': '0.005',
            'maker_fee_rate': '-0.0001',
            'mark_price': '165.28',
            'mark_price_round': '0.01',
            'mark_type': 'index',
            'name': 'SOL_USDT',
            'order_price_deviate': '0.1',
            'order_price_round': '0.01',
            'order_size_max': 1000000,
            'order_size_min': 1,
            'orderbook_id': 29464815860,
            'orders_limit': 100,
            'position_size': 2498808,
            'quanto_multiplier': '1',
            'ref_discount_rate': '0',
            'ref_rebate_rate': '0.2',
            'risk_limit_base': '200000',
            'risk_limit_max': '200000000',
            'risk_limit_step': '199800000',
            'taker_fee_rate': '0.00075',
            'trade_id': 92025578,
            'trade_size': 978820321,
            'type': 'direct'
        }
        """
        try:
            return self.swap_exchange.get_futures_contract_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0].to_dict()
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(round(log(1 / float(self.swap_exchange.get_futures_contract_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0].order_price_round), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            return max(0, int(round(log(1 / float(self.swap_exchange.get_futures_contract_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0].quanto_multiplier), 10), 0)))
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            info = self.swap_exchange.get_futures_contract_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0]
            return float(info.order_size_min) * float(info.quanto_multiplier)
        except:
            print(format_exc())
            return None

    # 获取永续合约币对(gateio只有usdt合约+btcusd币本位合约)
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol: usdt，usd，btc
        :return:
        """
        try:
            symbol_list = []
            _info = self.swap_exchange.list_futures_contracts_with_http_info(settle=base_symbol)[0]
            symbol_list.extend(i.name.replace('_', '').lower() for i in _info if i.in_delisting == False)
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol='usdt'):
        """
        :param symbol:
        :return:
        {
            available: "1844.088018",
            bonus: "0",
            currency: "USDT",
            enable_credit: True,
            history: {
                bonus_dnw: "0",
                bonus_offset: "0",
                dnw: "-117148.********",
                fee: "-107.**********",
                fund: "9560.**********",
                pnl: "106441.************",
                point_dnw: "597.********",
                point_fee: "-597.**********",
                point_refr: "0",
                refr: "0"
            },
            in_dual_mode: False,
            maintenance_margin: "0",
            order_margin: "0",
            point: "0.**********",
            position_initial_margin: "0",
            position_margin: "0",
            total: "0.***********",
            unrealised_pnl: "0"
            }
        """
        try:
            return float(self.swap_exchange.list_futures_accounts_with_http_info(settle='usdt')[0].available)
        except:
            print(format_exc())
            return None

    # 获取swap账户保证金率 TODO：待测试
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:

        """
        try:
            _info = self.swap_exchange.list_futures_accounts_with_http_info(settle='usdt')[0]
            return float(_info.available) / float(_info.maintenance_margin)
        except:
            print(format_exc())
            return None
        
    # 获取swap账户point余额
    def get_swap_point(self, symbol='usdt'):
        """
        返回spot point账户余额
        @return:

        """
        return float(self.swap_exchange.list_futures_accounts_with_http_info(settle='usdt')[0].point)

    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        :param symbol:
        :return:
        {
            adl_ranking: 5,
            close_order: None,
            contract: "BTC_USDT",
            cross_leverage_limit: "10",
            entry_price: "105242.4",
            history_pnl: "-0.************",
            history_point: "0",
            initial_margin: "1.***********",
            last_close_pnl: "-0.************",
            leverage: "0",
            leverage_max: "125",
            liq_price: "0",
            maintenance_margin: "0.***********",
            maintenance_rate: "0.004",
            margin: "0",
            mark_price: "105107.81",
            mode: "single",
            pending_orders: 0,
            realised_pnl: "-0.*********",
            realised_point: "0",
            risk_limit: "1000000",
            size: 1,
            unrealised_pnl: "-0.013459",
            update_time: 1748615099,
            user: 9131626,
            value: "10.510781"
            }
        """
        try:
            if symbol:
                positions = self.swap_exchange.get_position_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT')[0]
                quanto_multiplier = float(self.get_swap_instruments_info(symbol)['quanto_multiplier'])
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if float(positions.size) > 0 else 'sell',
                        'amount': abs(float(positions.size)) * quanto_multiplier,
                        'price': positions.entry_price,
                        'liquidation_price': positions.liq_price,
                    }
                ]
            else:
                positions = self.swap_exchange.list_positions_with_http_info(settle='usdt')[0]
                return [
                    {
                        'symbol': i.contract.replace('_USDT', '').lower(),
                        'direction': 'buy' if i.size > 0 else 'sell',
                        'amount': abs(float(i.size)) * float(self.get_swap_instruments_info(i.contract.replace('_USDT', '').lower())['quanto_multiplier']),
                        'price': i.entry_price,
                        'liquidation_price': i.liq_price,
                    }
                    for i in positions if i.size != 0
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        永续合约：
            U本位：
            [
              {'amend_text': '-',
                'auto_size': None,
                'biz_info': '-',
                'close': False,
                'contract': 'BTC_USDT',
                'create_time': 1748595156.32,
                'fill_price': '0',
                'finish_as': None,
                'finish_time': None,
                'iceberg': 0,
                'id': 36028801788780823,
                'is_close': False,
                'is_liq': False,
                'is_reduce_only': False,
                'left': 1,
                'mkfr': '0.0002',
                'price': '102000',
                'reduce_only': False,
                'refu': 0,
                'size': 1,
                'status': 'open',
                'stp_act': '-',
                'stp_id': 0,
                'text': 'web',
                'tif': 'gtc',
                'tkfr': '0.00045',
                'user': 9131626}
            ]
        """
        try:
            if symbol:
                open_orders = self.swap_exchange.list_futures_orders_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT', status='open')[0]
                return [
                    {
                        'order_id': i.id,
                        'symbol': symbol,
                        'direction': 'buy' if i.size > 0 else 'sell',
                        'order_type': 'limit',
                        'amount': abs(i.size),
                        'price': i.price,
                        'average_price': '',
                        'remain_amount': abs(i.left),
                    } for i in open_orders
                ]
            else:
                open_orders = self.swap_exchange.list_futures_orders_with_http_info(settle='usdt', status='open')[0]
                return [
                    {
                        'order_id': i.id,
                        'symbol': i.contract.replace('_USDT', '').lower(),
                        'direction': 'buy' if i.size > 0 else 'sell',
                        'order_type': 'limit',
                        'amount': abs(i.size),
                        'price': i.price,
                        'average_price': '',
                        'remain_amount': abs(i.left),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.swap_exchange.list_futures_orders_with_http_info(settle='usdt', status='open', contract=symbol.upper() + '_USDT')[0][0].id
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order(self, symbol, direction, amount, price=None, order_type='limit', close_position=False):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:
        :return:
        {
            id: 36028801790237273,
            user: 9131626,
            create_time: 1748598296.2,
            finish_time: None,
            finish_as: None,
            status: "open",
            contract: "BTC_USDT",
            size: 1,
            iceberg: 0,
            price: "102000",
            close: False,
            is_close: False,
            reduce_only: False,
            is_reduce_only: False,
            is_liq: False,
            tif: "gtc",
            left: 1,
            fill_price: "0",
            text: "api",
            tkfr: "0.00045",
            mkfr: "0.0002",
            refu: 0,
            auto_size: None,
            stp_id: 0,
            stp_act: "-",
            amend_text: "-",
            biz_info: "-"
            }
        """
        try:
            quanto_multiplier = float(
                self.get_swap_instruments_info(symbol)['quanto_multiplier'])
            contract_amount = amount / quanto_multiplier if direction == 'buy' else - amount / quanto_multiplier
            if order_type == 'limit':
                if close_position:
                    order_info = self.swap_exchange.create_futures_order_with_http_info(
                        settle='usdt', 
                        futures_order=FuturesOrder(
                            contract=symbol.upper() + '_USDT', 
                            price=0, 
                            size=contract_amount,
                            reduce_only=True,
                            tif='ioc',
                        )
                    )[0]
                else:
                    order_info = self.swap_exchange.create_futures_order_with_http_info(
                        settle='usdt', 
                        futures_order=FuturesOrder(
                            contract=symbol.upper() + '_USDT', 
                            price=price, 
                            size=contract_amount,
                        )
                    )[0]
            elif order_type == 'market':
                if close_position:
                    # gateio暂时没有按数量平仓的市价单
                    order_info = self.swap_exchange.create_futures_order_with_http_info(
                        settle='usdt', 
                        futures_order=FuturesOrder(
                            contract=symbol.upper() + '_USDT', 
                            price=0, 
                            size=contract_amount,
                            reduce_only=True,
                            tif='ioc',
                        )
                    )[0]
                else:
                    order_info = self.swap_exchange.create_futures_order_with_http_info(
                        settle='usdt', 
                        futures_order=FuturesOrder(
                            contract=symbol.upper() + '_USDT', 
                            price=0, 
                            size=contract_amount,
                            tif='ioc',
                        )
                    )[0]
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info.id,
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': order_info.price,
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 查询订单
    def get_swap_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
                id: 36028801788860932,
                user: 9131626,
                create_time: 1748595384.701,
                finish_time: None,
                finish_as: None,
                status: "open",
                contract: "BTC_USDT",
                size: -1,
                iceberg: 0,
                price: "106000",
                close: False,
                is_close: False,
                reduce_only: False,
                is_reduce_only: False,
                is_liq: False,
                tif: "gtc",
                left: -1,
                fill_price: "0",
                text: "web",
                tkfr: "0.00045",
                mkfr: "0.0002",
                refu: 0,
                auto_size: None,
                stp_id: 0,
                stp_act: "-",
                amend_text: "-",
                biz_info: "-"
                }
        """
        try:
            fill_info = self.swap_exchange.get_futures_order_with_http_info(settle='usdt', order_id=order_id)[0]
            fee = 0
            if fill_info.status == 'finished':
                order_history = self.swap_exchange.get_my_trades_with_http_info(settle='usdt', contract=symbol.upper() + '_USDT', order=order_id)[0]
                fee += sum(float(i.fee) for i in order_history)     # 目前使用vip费率
            quanto_multiplier = float(self.get_swap_instruments_info(symbol)['quanto_multiplier'])
            return {
                'exchange': self.exchange_name,
                'order_id': fill_info.id,
                'symbol': symbol,
                'direction': 'buy' if fill_info.size > 0 else 'sell',
                'order_type': 'limit',
                'amount': abs(fill_info.size) * quanto_multiplier,
                'price': fill_info.price,
                'average_price': fill_info.fill_price,
                'remain_amount': abs(fill_info.left) * quanto_multiplier,
                'fee': fee,     # usdt手续费+point手续费
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {
            'amend_text': '-',
            'auto_size': None,
            'biz_info': '-',
            'close': False,
            'contract': 'BTC_USDT',
            'create_time': 1748595384.699,
            'fill_price': '0',
            'finish_as': 'cancelled',
            'finish_time': 1748596607.408,
            'iceberg': 0,
            'id': 36028801788860932,
            'is_close': False,
            'is_liq': False,
            'is_reduce_only': False,
            'left': -1,
            'mkfr': '0.0002',
            'price': '106000',
            'reduce_only': False,
            'refu': 0,
            'size': -1,
            'status': 'finished',
            'stp_act': '-',
            'stp_id': 0,
            'text': 'web',
            'tif': 'gtc',
            'tkfr': '0.00045',
            'user': 9131626
        }
        """
        try:
            cancel_order_info = self.swap_exchange.cancel_futures_order_with_http_info(settle='usdt', order_id=order_id)[0].to_dict()
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info.finish_as == 'cancelled' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO：币本位合约
    # region =======================================coin swap

    # endregion
