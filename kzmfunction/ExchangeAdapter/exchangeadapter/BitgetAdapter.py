"""
备注说明：
    官方sdk：https://github.com/BitgetLimited/v3-bitget-api-sdk/tree/master/bitget-python-sdk-api
    
    
"""
from .ExchangeAdapter import ExchangeAdapter
from traceback import format_exc
from math import ceil, log
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ..api_config import bitget_api_config


class BitgetAdapter(ExchangeAdapter):
    def __init__(self, account='<EMAIL>'):
        self._api_config = bitget_api_config.api_config
        self.account = account
        self.exchange_name = 'bitget'
        # 创建交易所
        from ccxt import bitget
        self.exchange = bitget()
        if account:
            self.exchange.apiKey = self._api_config[account]['api_key']
            self.exchange.secret = self._api_config[account]['secret_key']
            self.exchange.password = self._api_config[account]['passphrase']
        else:
            self.exchange.apiKey = self._api_config['查询']['api_key']
            self.exchange.secret = self._api_config['查询']['secret_key']
            self.exchange.password = self._api_config['查询']['passphrase']

    # region =======================================spot
    # 获取当前最新价格
    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        return float(self.exchange.publicSpotGetV2SpotMarketTickers(params={'symbol': symbol.upper() + 'USDT'})['data'][0]['lastPr'])

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
        """
        try:
            return float(self.exchange.publicSpotGetV2SpotMarketOrderbook(params={'symbol': symbol.upper() + 'USDT'})['data']['bids'][0][0])
        except:
            print(format_exc())
            return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:
        """
        try:
            return float(self.exchange.publicSpotGetV2SpotMarketOrderbook(params={'symbol': symbol.upper() + 'USDT'})['data']['asks'][0][0])
        except:
            print(format_exc())
            return None

    # 获取spot最优挂单
    def get_spot_best_orderbook(self, symbol):
        """
        获取币对的最优挂单
        :param symbol:
        :return:
          {
            "open": "2649.99",
            "symbol": "ETHUSDT",
            "high24h": "2694.68",
            "low24h": "2555.34",
            "lastPr": "2581.59",
            "quoteVolume": "122005028.5979",
            "baseVolume": "46501.9934",
            "usdtVolume": "122005028.597849",
            "ts": "1724205299120",
            "bidPr": "2581.58",
            "askPr": "2581.59",
            "bidSz": "23.8806",
            "askSz": "16.2938",
            "openUtc": "2572.7",
            "changeUtc24h": "0.00345",
            "change24h": "0.00261"
          }
        """
        try:
            raw_orderbook = self.exchange.publicSpotGetV2SpotMarketTickers(
                params={'symbol': symbol.upper() + 'USDT'})['data'][0]
            return {
                'bid': [raw_orderbook['bidPr'], raw_orderbook['bidSz']],
                'ask': [raw_orderbook['askPr'], raw_orderbook['askSz']]
            }
        except:
            print(format_exc())
            return None

    # 获取orderbook
    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        """
        try:
            raw_orderbook = self.exchange.publicSpotGetV2SpotMarketOrderbook(
                params={'symbol': symbol.upper() + 'USDT'})['data']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'][: limit],
                'asks': raw_orderbook['asks'][: limit]
            }
        except:
            print(format_exc())
            return None

    # TODO：
    def get_spot_kline(self):
        pass

    # 获取现货信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {
            symbol: "ZKUSDT",
            baseCoin: "ZK",
            quoteCoin: "USDT",
            minTradeAmount: "0",
            maxTradeAmount: "1********00",
            takerFeeRate: "0.001",
            makerFeeRate: "0.001",
            pricePrecision: "4",
            quantityPrecision: "2",
            quotePrecision: "6",
            status: "online",
            minTradeUSDT: "1",
            buyLimitPriceRatio: "0.02",
            sellLimitPriceRatio: "0.02",
            areaSymbol: "no",
            orderQuantity: "200"
            }
        """
        try:
            return self.exchange.publicSpotGetV2SpotPublicSymbols(params={'symbol': symbol.upper() + 'USDT'})['data'][0]
        except:
            print(format_exc())
            return None
        
    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(self.exchange.publicSpotGetV2SpotPublicSymbols(params={'symbol': symbol.upper() + 'USDT'})['data'][0]['pricePrecision'])
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(self.exchange.publicSpotGetV2SpotPublicSymbols(params={'symbol': symbol.upper() + 'USDT'})['data'][0]['quantityPrecision'])
        except:
            print(format_exc())
            return None

    # 获取下单价格精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_price_tick_size2(self, symbol, base_symbol):
        """
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(self.exchange.publicSpotGetV2SpotPublicSymbols(params={'symbol': symbol.upper() + base_symbol.upper()})['data'][0]['pricePrecision'])
        except:
            print(format_exc())
            return None

    # 获取下单数量精度(适用于交叉币种,比如cake/bnb)
    def get_spot_order_amount_tick_size2(self, symbol, base_symbol):
        """
        获取下单数量精度
        :param symbol:
        :param base_symbol:
        :return:
        """
        try:
            return int(self.exchange.publicSpotGetV2SpotPublicSymbols(params={'symbol': symbol.upper() + base_symbol.upper()})['data'][0]['quantityPrecision'])
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_spot_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.exchange.publicSpotGetV2SpotPublicSymbols(params={'symbol': symbol.upper() + 'USDT'})['data'][0]
            _min_amount = float(_info['minTradeAmount'])
            _min_nominal = float(_info['minTradeUSDT'])
            _price = self.get_spot_last_price(symbol)
            _amount_precision = int(_info['quantityPrecision'])
            _min_amount2 = ceil(_min_nominal * 1.05 / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            return max(_min_amount, _min_amount2)
        except:
            print(format_exc())
            return None
        
    # 获取现货币对
    def get_spot_instruments_symbols(self, base_symbol='usdt'):
        """`

        :return:
        """
        try:
            symbol_list = []
            _info = self.exchange.publicSpotGetV2SpotPublicSymbols()['data']
            symbol_list.extend([i['symbol'].lower()
                               for i in _info if i['status'] == 'online'])
            base_symbol_len = len(base_symbol)
            symbol_list = [
                i for i in symbol_list if i[-base_symbol_len:] == base_symbol]
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(单币种)
    def get_spot_account_single_asset(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return float((self.exchange.privateSpotGetV2SpotAccountAssets(params={'coin': symbol.upper()})['data'][0]['available']))
        except:
            print(format_exc())
            return None

    # 获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
        [
          {
            "coin": "BGB",
            "available": "286.********",
            "limitAvailable": "0",
            "frozen": "0.********",
            "locked": "0.********",
            "uTime": "*************"
          },
          {
            "coin": "USDT",
            "available": "10143.********",
            "limitAvailable": "0",
            "frozen": "0.********",
            "locked": "0.********",
            "uTime": "*************"
          },
          {
            "coin": "ETH",
            "available": "0.********",
            "limitAvailable": "0",
            "frozen": "0.********",
            "locked": "0.********",
            "uTime": "*************"
          },
          {
            "coin": "BLAST",
            "available": "4033692.********",
            "limitAvailable": "0",
            "frozen": "0.********",
            "locked": "0.********",
            "uTime": "*************"
          }
        ]
        """
        try:
            balance = self.exchange.privateSpotGetV2SpotAccountAssets()['data']
            return {i['coin'].lower(): i['available'] for i in balance if float(i['available']) > 0}
        except Exception as e:
            print(format_exc())
            return None

    # 获取spot账户挂单
    def get_spot_open_order(self, symbol):
        """
        :param symbol:
        :return:
            {
              "userId": "**********",
              "symbol": "ETHUSDT",
              "orderId": "1209993674764267523",
              "clientOid": "d1713a40-abb5-435e-ac7b-dabb6296a73c",
              "priceAvg": "2400",
              "size": "0.01",
              "orderType": "limit",
              "side": "buy",
              "status": "live",
              "basePrice": "0",
              "baseVolume": "0",
              "quoteVolume": "0",
              "enterPointSource": "WEB",
              "orderSource": "normal",
              "triggerPrice": "None",
              "tpslType": "normal",
              "cTime": "*************",
              "uTime": "1724210893400"
            }
        """
        try:
            open_orders = self.exchange.privateSpotGetV2SpotTradeUnfilledOrders(
                params={'symbol': symbol.upper() + 'USDT'})['data']
            return [{
                'order_id': order['userId'],
                'symbol': symbol,
                'direction': order['side'],
                'amount': order['size'],
                'price': order['priceAvg'],
                'order_type': order['orderType'],
                'average_price': '',
                'remain_amount': '',
            } for order in open_orders]
        except:
            print(format_exc())
            return None

    # 下单 (bitget暂时没有市价单)
    def place_spot_order(self, symbol, direction, amount, price=float, order_type='limit', params={'quoteOrderQty': None}):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param params:
        :return:
            {
          "orderId": "1209995047618687002",
          "clientOid": "608b4eda-46cc-4481-aa0e-e7768018a339"
        }
        """
        try:
            if order_type == 'limit':
                order_info = self.exchange.privateSpotPostV2SpotTradePlaceOrder(params={'symbol': symbol.upper(
                ) + 'USDT', 'side': direction, 'orderType': 'limit', 'price': price, 'size': amount, 'force': 'gtc'})['data']
            elif order_type == 'market':
                # bitget暂时没有市价单
                return None
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 下单 spot下单(适用于交叉币种,比如eth/btc, bitget暂时没有市价单)
    def place_spot_order2(self, symbol, base_symbol, direction, amount, price='', order_type='limit'):
        try:
            if order_type == 'limit':
                order_info = self.exchange.privateSpotPostV2SpotTradePlaceOrder(params={'symbol': symbol.upper(
                ) + base_symbol.upper(), 'side': direction, 'orderType': 'limit', 'price': price, 'size': amount, 'force': 'gtc'})['data']
            elif order_type == 'market':
                # (bitget暂时没有市价单)
                return None
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol + '/' + base_symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_spot_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
              "userId": "**********",
              "symbol": "ETHUSDT",
              "orderId": "1210001155527749658",
              "clientOid": "83f31fdd-fedd-4361-9568-9006cda000e5",
              "price": "2600.****************",
              "size": "0.01********000000",
              "orderType": "limit",
              "side": "buy",
              "status": "filled",
              "priceAvg": "2590.37********000000",
              "baseVolume": "0.01********000000",
              "quoteVolume": "25.9037********0000",
              "enterPointSource": "API",
              "feeDetail": "{\"newFees\":{\"c\":0,\"d\":-0.0221992072844136,\"deduction\":false,\"r\":0,\"t\":-0.00001,\"totalDeductionFee\":0},\"BGB\":{\"deduction\":true,\"feeCoinCode\":\"BGB\",\"totalDeductionFee\":-0.0221992072844136,\"totalFee\":-0.0221992072844136}}",
              "orderSource": "normal",
              "tpslType": "normal",
              "triggerPrice": "None",
              "quoteCoin": "USDT",
              "baseCoin": "ETH",
              "cTime": "1724212676889",
              "uTime": "1724212676990"
            }
        """
        try:
            order_info = self.exchange.privateSpotGetV2SpotTradeOrderInfo(
                params={'orderId': order_id})['data'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': order_info['side'],
                'order_type': order_info['orderType'],
                'amount': order_info['size'],
                'price': order_info['price'],
                'average_price': order_info['priceAvg'],
                'remain_amount': float(order_info['size']) - float(order_info['baseVolume']),
                'fee': abs(order_info['feeDetail']['BGB']['totalDeductionFee'])
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息2
    def get_spot_order_info2(self, symbol, base_symbol, order_id):
        try:
            order_info = self.exchange.privateSpotGetV2SpotTradeOrderInfo(
                params={'orderId': order_id})['data'][0]
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol + '/' + base_symbol,
                'direction': order_info['side'],
                'order_type': order_info['orderType'],
                'amount': order_info['size'],
                'price': order_info['price'],
                'average_price': order_info['priceAvg'],
                'remain_amount': float(order_info['size']) - float(order_info['baseVolume']),
                'fee': abs(order_info['feeDetail']['BGB']['totalDeductionFee'])
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_spot_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
              "code": "00000",
              "msg": "success",
              "requestTime": "1724213092220",
              "data": "ETHUSDT_SPBL"
            }
        """
        try:
            order_info = self.exchange.privateSpotPostSpotV1TradeCancelSymbolOrder(
                params={'symbol': symbol.upper() + 'USDT_SPBL', 'orderId': order_id})
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if order_info['msg'] == 'success' else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO：交割合约
    # region =======================================future
    # 获取合约当期instrument_id
    def get_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        """
        根据输入的symbol获取当期周期(当周/次周/季度/次季度)的合约id
        对于季度合约:
            如果不指定contract_type,则返回当周,次周,季度,次季度 四个instrument_id组成的list
            如果指定contract_type,则返回单个instrument_id的str
        @param symbol:  BTC
        @param margin_type:  币本位coin or U本位usdt
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:
        """
        instrument_id = self.exchange.get_products()
        margin_type2 = {'coin': 'USD', 'usdt': 'USDT'}.get(margin_type)
        if contract_type:
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['alias'] == contract_type and i['quote_currency'] == margin_type2:
                    return i['instrument_id']
        else:
            instrument_id_list = []
            for i in instrument_id:
                if i['underlying_index'] == symbol.upper() and i['quote_currency'] == margin_type2:
                    instrument_id_list.append(i['instrument_id'])
            return instrument_id_list

    # 获取future盘口买1价
    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的买1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['bids'][0][0])

    # 获取future盘口卖1价
    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        """
        返回当前future盘口的卖1价格
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @param contract_type:   this_week, next_week, quarter, bi_quarter
        @return:

        """
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id
        if margin_type == 'coin':  # 币本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])
        elif margin_type == 'usdt':  # U本位
            return float(self.exchange.get_depth(instrument_id)['asks'][0][0])

    # # 获取future-K线
    # def get_future_kline(self, symbol, margin_type='usdt', contract_type='quarter', period='15min', start='', end=''):
    #     """
    #     获取future-K线
    #     @param symbol:  btc
    #     @param margin_type: 币本位(coin)或U本位(usdt)
    #     @param contract_type:   this_week, next_week, quarter, bi_quarter
    #     @param period:  时间周期, min、hour、day、week  okex输入参数以秒为单位，默认值60。如[60/180/300/900/1800/3600/7200/14400/21600/43200/86400/604800]
    #     @param start:  str
    #     @param end:   str
    #     @return:    future：# 300根K线
    #     """
    #     # 获取K线周期，转为秒
    #     if 'min' in period:
    #         granularity = str(get_number(period) * 60).split('.')[0]
    #     elif 'hour' in period:
    #         granularity = str(get_number(period) * 3600).split('.')[0]
    #     elif 'day' in period:
    #         granularity = str(get_number(period) * 86400).split('.')[0]
    #     elif 'week' in period:
    #         granularity = str(get_number(period) * 604800).split('.')[0]
    #     else:
    #         granularity = '900'  # 默认15min
    #
    #     # 获取起始时间
    #     if start and end:
    #         start = (pd.to_datetime(start) - timedelta(hours=8)).isoformat("T") + "Z"
    #         end = (pd.to_datetime(end) - timedelta(hours=8)).isoformat("T") + "Z"
    #
    #     # =======================================交割合约K线
    #     instrument_id = self.get_instrument_id(symbol, margin_type, contract_type)  # 获取合约id
    #     if margin_type == 'coin':  # 币本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     elif margin_type == 'usdt':  # U本位
    #         kline = self.exchange.get_kline(instrument_id, granularity=granularity, start=start, end=end)
    #     else:
    #         kline = pd.DataFrame()
    #     # 转为Dataframe，转为北京时区
    #     kline = pd.DataFrame(kline, columns=['candle_begin_time', 'open', 'high', 'low', 'close', 'cont', 'volume'])
    #     kline['candle_begin_time'] = pd.to_datetime(kline['candle_begin_time']) + timedelta(hours=8)
    #
    #     return kline

    # <editor-fold desc="# ====================== todo future">
    # 获取future账户余额
    def get_future_account(self, symbol, margin_type='usdt'):
        """
        返回future账户的余额（币或USDT）
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        # ======================================= 交割合约:
            币本位：
                {
                  'equity': '105.********',
                  'margin': '106.********',
                  'realized_pnl': '0',
                  'unrealized_pnl': '1.********',
                  'margin_ratio': '0.********',     保证金率
                  'margin_mode': 'crossed',
                  'total_avail_balance': '104.********',
                  'margin_frozen': '106.********',
                  'margin_for_unfilled': '0',
                  'liqui_mode': 'tier',
                  'maint_margin_ratio': '0.02',
                  'liqui_fee_rate': '0.00035',
                  'can_withdraw': '0',  可划转数量
                  'underlying': 'BSV-USD',
                  'currency': 'BSV'
                }
            U本位：
                {
                  'total_avail_balance': '66.********',
                  'contracts': None,
                  'equity': '66.********',
                  'margin_mode': 'fixed',
                  'auto_margin': '0',
                  'liqui_mode': 'tier',
                  'can_withdraw': '66.********',
                  'currency': 'USDT'
                }
        """
        if margin_type == 'coin':  # 币本位
            return self.exchange.get_coin_account(symbol.upper() + '-USD')
        elif margin_type == 'usdt':  # U本位
            return self.exchange.get_coin_account(symbol.upper() + '-USDT')

    # 获取future持仓信息
    def get_future_position(self, symbol, margin_type='usdt'):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:    返回持仓列表
        交割合约：
            币本位：
                [{
                  'long_qty': '4145',
                  'long_avail_qty': '4145',
                  'long_avg_cost': '195.********',
                  'long_settlement_price': '194.08',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_avg_cost': '195.********',
                  'short_settlement_price': '195.********',
                  'liquidation_price': '132.61',
                  'instrument_id': 'BSV-USD-200626',
                  'leverage': '2',
                  'created_at': '2020-04-02T23:37:37.220Z',
                  'updated_at': '2020-04-18T08:00:10.913Z',
                  'margin_mode': 'crossed',
                  'short_margin': '0.0',
                  'short_pnl': '0.0',
                  'short_pnl_ratio': '-0.03896512',
                  'short_unrealised_pnl': '0.0',
                  'long_margin': '104.08296505',
                  'long_pnl': '3.87219199',
                  'long_pnl_ratio': '0.03652354',
                  'long_unrealised_pnl': '5.40579291',
                  'long_settled_pnl': '-1.53360092',
                  'short_settled_pnl': '0',
                  'last': '199.14'
                }]
            U本位：
                [{
                  'long_qty': '0',
                  'long_avail_qty': '0',
                  'long_margin': '0',
                  'long_liqui_price': '0',
                  'long_pnl_ratio': '0',
                  'long_avg_cost': '0',
                  'long_settlement_price': '0',
                  'realised_pnl': '0',
                  'short_qty': '0',
                  'short_avail_qty': '0',
                  'short_margin': '0',
                  'short_liqui_price': '0',
                  'short_pnl_ratio': '0',
                  'short_avg_cost': '0',
                  'short_settlement_price': '0',
                  'instrument_id': 'BSV-USDT-200626',
                  'long_leverage': '10',
                  'short_leverage': '10',
                  'created_at': '1970-01-01T00:00:00.000Z',
                  'updated_at': '1970-01-01T00:00:00.000Z',
                  'margin_mode': 'fixed',
                  'short_margin_ratio': '0',
                  'short_maint_margin_ratio': '0',
                  'short_pnl': '0',
                  'short_unrealised_pnl': '0',
                  'long_margin_ratio': '0',
                  'long_maint_margin_ratio': '0',
                  'long_pnl': '0',
                  'long_unrealised_pnl': '0',
                  'long_settled_pnl': '0',
                  'short_settled_pnl': '0',
                  'last': '199.53'
                }]
        """
        # =======================================交割合约持仓信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取交割合约id

        for i in instrument_id:
            temp_position = self.exchange.get_specific_position(i)['holding']

            if temp_position[0]['long_qty'] != '0' or temp_position[0]['short_qty'] != '0':
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单信息
    def get_future_open_order(self, symbol, margin_type='usdt'):
        """
        返回future账户的挂单信息
        @param symbol:  btc
        @param margin_type: 币本位(coin)或U本位(usdt)
        @return:
        交割合约：
            币本位：
            [{'instrument_id': 'BSV-USD-200501',
                  'size': '1',
                  'timestamp': '2020-04-19T07:00:04.943Z',
                  'filled_qty': '0',    # 成交数量
                  'fee': '0',
                  'order_id': '4754217692783617',
                  'price': '190',   委托价格
                  'price_avg': '0',     成交均价
                  'status': '0',
                  'state': '0',     -2：失败-1：撤单成功0：等待成交1：部分成交2：完全成交3：下单中4：撤单中
                  'type': '1',      1:开多2:开空3:平多4:平空
                  'contract_val': '10',     合约面值
                  'leverage': '2',
                  'client_oid': '',
                  'pnl': '0',   收益
                  'order_type': '0'
            }]
            U本位：
                [{'instrument_id': 'BSV-USDT-200501',
                   'size': '1',
                   'timestamp': '2020-04-19T07:15:15.196Z',
                   'filled_qty': '0',
                   'fee': '0',
                   'order_id': '4754277346794497',
                   'price': '190',
                   'price_avg': '0',
                   'status': '0',
                   'state': '0',
                   'type': '1',     1:开多2:开空3:平多4:平空
                   'contract_val': '1',
                   'leverage': '10',
                   'client_oid': '',
                   'pnl': '0',
                   'order_type': '0'
                }]
        """
        # =======================================交割合约挂单信息
        open_position = []
        instrument_id = self.get_instrument_id(symbol, margin_type)  # 获取合约id

        for i in instrument_id:
            temp_position = self.exchange.get_order_list(i, state='6')[
                'order_info']
            if temp_position:
                open_position.append(temp_position[0])
        return open_position

    # 获取future挂单订单号
    def get_future_open_order_id(self, symbol, margin_type):
        """
        返回future账户的挂单订单号
        :param symbol:
        :param margin_type:
        :return:
        """
        # return self.get_future_open_order(symbol, margin_type)
        return self.get_future_open_order(symbol, margin_type)[0]['order_id']

    # TODO 根据订单号获取future订单详细信息
    def get_future_order_info(self, order_id, instrument_id=''):
        """
        @param order_id:
        @param instrument_id:
        @return:
        交割合约订单信息：
            {
              'instrument_id': 'BSV-USD-200626',
              'size': '1',
              'timestamp': '2020-04-22T14:46:12.585Z',
              'filled_qty': '0',    成交数量
              'fee': '0',
              'order_id': '4773037511302145',
              'price': '180',
              'price_avg': '0',
              'status': '0',
              'state': '0',     -2:失败 -1:撤单成功 0:等待成交 1:部分成交 2:完全成交 3:下单中 4:撤单中
              'type': '1',      1:开多 2:开空 3:平多 4:平空
              'contract_val': '10',     合约面值
              'leverage': '1',
              'client_oid': '',
              'pnl': '0',   收益
              'order_type': '0'
            }
        """

        return self.exchange.get_order_info(instrument_id, order_id)

    # 获取future可平仓数量
    def get_future_avail_amount(self, symbol, margin_type='usdt'):
        """
        获取future可平仓数量,只针对持有单一合约有效，若有平仓的挂单，则不含这部分持仓
        @param symbol: btc
        @param margin_type: coin or usdt
        @return:
        """
        order_info = self.get_future_position(symbol, margin_type)[0]
        if order_info['long_avail_qty'] != '0':
            return float(order_info['long_avail_qty'])
        elif order_info['short_avail_qty'] != '0':
            return float(order_info['short_avail_qty'])

    # future下单
    def place_future_order(self, symbol, amount, direction, order_type='limit', margin_type='usdt', contract_type='quarter', price=''):
        """

        @param symbol: btc
        @param amount:
        @param direction: buy,sell,close_buy,close_sell
        @param price:
        @param order_type: limit market
        @param margin_type: coin,usdt
        @param contract_type: this_week, next_week, quarter, bi_quarter
        @return:
        参数名	参数类型	描述
            order_id	String	订单ID，下单失败时，此字段值为-1
            client_oid	String	由您设置的订单ID来识别您的订单
            error_code	String	错误码，下单成功时为0，下单失败时会显示相应错误码
            error_message	String	错误信息，下单成功时为空，下单失败时会显示错误信息
            result	Boolean	调用接口返回结果
        """
        type_dict = {'buy': '1', 'sell': '2',
                     'close_buy': '3', 'close_sell': '4'}
        instrument_id = self.get_instrument_id(
            symbol, margin_type, contract_type)  # 获取合约id

        if order_type == 'limit':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=str(price), size=str(amount))
        elif order_type == 'market':
            order_info = self.exchange.take_order(
                instrument_id=instrument_id, type=type_dict[direction], price=price, order_type='4', size=str(amount))
        else:
            order_info = None
        return order_info

    # future 撤单
    def cancel_future_order(self, order_id, instrument_id):
        """

        @param order_id: str 订单号
        @param instrument_id: str
        @return:
        """
        cancel_order_info = self.exchange.revoke_order(
            instrument_id, order_id=order_id)

        return cancel_order_info

    # endregion

    # region =======================================usdt swap
    # 获取最新价格
    def get_swap_latest_price(self, symbol):
        try:
            return float(self.exchange.publicMixGetV2MixMarketTicker(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]['lastPr'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.publicMixGetV2MixMarketTicker(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]['bidPr'])
        except:
            print(format_exc())
            return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        try:
            return float(self.exchange.publicMixGetV2MixMarketTicker(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]['askPr'])
        except:
            print(format_exc())
            return None

    # 获取swap最优挂单
    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
          {
            "symbol": "ETHUSDT",
            "lastPr": "2583.72",
            "askPr": "2583.59",
            "bidPr": "2583.26",
            "bidSz": "4.32",
            "askSz": "11.05",
            "high24h": "2617.17",
            "low24h": "2533.95",
            "ts": "1724249858156",
            "change24h": "0.00412",
            "baseVolume": "773417.27",
            "quoteVolume": "1995877707.5654",
            "usdtVolume": "1995877707.5654",
            "openUtc": "2571.05",
            "changeUtc24h": "0.00493",
            "indexPrice": "2586.061396",
            "fundingRate": "0.000039",
            "holdingAmount": "222189.91",
            "deliveryStartTime": "None",
            "deliveryTime": "None",
            "deliveryStatus": "",
            "open24h": "2606.01"
          }
          """
        try:
            raw_orderbook = self.exchange.publicMixGetV2MixMarketTicker(
                params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]
            return {
                'bid': [raw_orderbook['bidPr'], raw_orderbook['bidSz']],
                'ask': [raw_orderbook['askPr'], raw_orderbook['askSz']]
            }
        except:
            print(format_exc())
            return None

    # 获取orderbook
    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "asks": [
            [
              "2596.01",
              "19.26"
            ],
            [
              "2596.03",
              "11.26"
            ],
            [
              "2596.04",
              "8.13"
            ],
            ...
          ],
          "bids": [
            [
              "2596.00",
              "0.03"
            ],
            [
              "2595.69",
              "0.09"
            ],
            [
              "2595.32",
              "0.80"
            ],
            ...
          ],
          "ts": "1724250785662",
          "scale": "0.01",
          "precision": "scale0",
          "isMaxPrecision": "NO"
        }
        """
        try:
            raw_orderbook = self.exchange.publicMixGetV2MixMarketMergeDepth(
                params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES', 'limit': limit})['data']
            return {
                'symbol': symbol,
                'bids': raw_orderbook['bids'],
                'asks': raw_orderbook['asks']
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        {
          "symbol": "ETHUSDT",
          "fundingRate": "0.000035"
        }
        """
        try:
            return float(self.exchange.publicMixGetV2MixMarketCurrentFundRate(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]['fundingRate'])
        except:
            print(format_exc())
            return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大1000
        :return: list越往后时间越靠近现在
            [
              {
                "symbol": "BTCUSDT",
                "fundingRate": "-0.000008",
                "fundingTime": "1724227200000"
              },
              {
                "symbol": "BTCUSDT",
                "fundingRate": "-0.000089",
                "fundingTime": "1724198400000"
              },
              {
                "symbol": "BTCUSDT",
                "fundingRate": "0.000019",
                "fundingTime": "1724169600000"
              },
              {
                "symbol": "BTCUSDT",
                "fundingRate": "0.000024",
                "fundingTime": "1724140800000"
              },
              {
                "symbol": "BTCUSDT",
                "fundingRate": "-0.000031",
                "fundingTime": "1724112000000"
              }
            ]
        """
        try:
            funding_rates = self.exchange.publicMixGetV2MixMarketHistoryFundRate(
                params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES', 'pageSize': limit})['data']
            funding_rates.reverse()
            return {
                'symbol': symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # 获取永续合约持仓量(usdt)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            info = self.exchange.publicMixGetV2MixMarketTicker(
                params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]
            return float(info['holdingAmount']) * float(info['lastPr'])
        except:
            print(format_exc())
            return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
          {
            "symbol": "ETHUSDT",
            "baseCoin": "ETH",
            "quoteCoin": "USDT",
            "buyLimitPriceRatio": "0.01",
            "sellLimitPriceRatio": "0.01",
            "feeRateUpRatio": "0.005",
            "makerFeeRate": "0.0002",
            "takerFeeRate": "0.0006",
            "openCostUpRatio": "0.01",
            "supportMarginCoins": [
              "USDT"
            ],
            "minTradeNum": "0.01",
            "priceEndStep": "1",
            "volumePlace": "2",
            "pricePlace": "2",
            "sizeMultiplier": "0.01",
            "symbolType": "perpetual",
            "minTradeUSDT": "5",
            "maxSymbolOrderNum": "200",
            "maxProductOrderNum": "400",
            "maxPositionNum": "150",
            "symbolStatus": "normal",
            "offTime": "-1",
            "limitOpenTime": "-1",
            "deliveryTime": "",
            "deliveryStartTime": "",
            "deliveryPeriod": "",
            "launchTime": "",
            "fundInterval": "8",
            "minLever": "1",
            "maxLever": "100",
            "posLimit": "0.05",
            "maintainTime": ""
          }
        """
        try:
            return self.exchange.publicMixGetV2MixMarketContracts(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]
        except:
            print(format_exc())
            return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        try:
            return int(self.exchange.publicMixGetV2MixMarketContracts(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]['pricePlace'])
        except:
            print(format_exc())
            return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        获取下单数量精度
        :param symbol:
        :return:
        """
        try:
            return int(self.exchange.publicMixGetV2MixMarketContracts(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]['volumePlace'])
        except:
            print(format_exc())
            return None

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        获取最小下单数量
        :param symbol:
        :return:
        """
        try:
            _info = self.exchange.publicMixGetV2MixMarketContracts(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data'][0]
            _min_amount = float(_info['minTradeNum'])
            _min_nominal = float(_info['minTradeUSDT'])
            _price = self.get_swap_latest_price(symbol)
            _amount_precision = int(_info['volumePlace'])
            _min_amount2 = ceil(_min_nominal / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            return max(_min_amount, _min_amount2)
        except:
            print(format_exc())
            return None
        
    # 获取永续合约币对
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        :param base_symbol:
                USDT-FUTURES USDT专业合约
                COIN-FUTURES 混合合约
                USDC-FUTURES USDC专业合约
        :return:
        """
        try:
            symbol_list = []
            _info = self.exchange.publicMixGetV2MixMarketContracts(
                params={'productType': base_symbol.upper() + '-FUTURES'})['data']
            if base_symbol == 'usdc':
                symbol_list.extend(
                    [i['symbol'].lower().replace('perp', 'usdc') for i in _info])
            else:
                symbol_list.extend([i['symbol'].lower() for i in _info])
            return symbol_list
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol='usdt'):
        """
        :param symbol:
        :return:

        """
        try:
            return float(self.exchange.privateMixGetV2MixAccountAccounts(params={'productType': 'USDT-FUTURES'})['data'][0]['available'])
        except:
            print(format_exc())
            return None

    # 获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
            {
            marginCoin: "USDT",
            locked: "0",
            available: "273.********",
            crossedMaxAvailable: "273.********",
            isolatedMaxAvailable: "273.********",
            maxTransferOut: "273.********",
            accountEquity: "273.********",
            usdtEquity: "273.************",
            btcEquity: "0.************",
            crossedRiskRate: "0.***********",
            unrealizedPL: "-0.0048",
            coupon: "0",
            crossedUnrealizedPL: "",
            isolatedUnrealizedPL: "",
            grant: "0"
            }
        """
        try:
            return float(self.exchange.privateMixGetV2MixAccountAccounts(params={'productType': 'USDT-FUTURES'})['data'][0]['crossedRiskRate'])
        except:
            print(format_exc())
            return None
        
    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        :param symbol:
        :return:

        """
        try:
            if symbol:
                positions = self.exchange.privateMixGetV2MixPositionSinglePosition(
                    params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES', 'marginCoin': 'USDT'})['data']
                return [
                    {
                        'symbol': symbol,
                        'direction': 'buy' if i['holdSide'] == 'long' else 'sell',
                        'amount': i['available'],
                        'price': i['openPriceAvg'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
            else:
                positions = self.exchange.privateMixGetV2MixPositionAllPosition(
                    params={'productType': 'USDT-FUTURES', 'marginCoin': 'USDT'})['data']
                return [
                    {
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': 'buy' if i['holdSide'] == 'long' else 'sell',
                        'amount': i['available'],
                        'price': i['openPriceAvg'],
                        'liquidation_price': i['liquidationPrice'],
                    } for i in positions
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
            [
              {
                "symbol": "ETHUSDT",
                "size": "0.01",
                "orderId": "1210171820464578565",
                "clientOid": "1210171820468772872",
                "baseVolume": "0",
                "fee": "0",
                "price": "2500",
                "priceAvg": "",
                "status": "live",
                "side": "buy",
                "force": "gtc",
                "totalProfits": "0",
                "posSide": "long",
                "marginCoin": "USDT",
                "quoteVolume": "0",
                "leverage": "10",
                "marginMode": "crossed",
                "enterPointSource": "ios",
                "tradeSide": "open",
                "posMode": "hedge_mode",
                "orderType": "limit",
                "orderSource": "normal",
                "presetStopSurplusPrice": "",
                "presetStopLossPrice": "",
                "reduceOnly": "NO",
                "cTime": "1724253366581",
                "uTime": "1724253366596"
              }
            ]
        """
        try:
            if symbol:
                open_orders = self.exchange.privateMixGetV2MixOrderOrdersPending(
                    params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data']['entrustedList']
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': symbol,
                        'direction': i['side'],
                        'order_type': i['orderType'],
                        'amount': i['size'],
                        'price': i['price'],
                        'average_price': i['priceAvg'],
                        'remain_amount': float(i['size']) - float(i['baseVolume']),
                    } for i in open_orders
                ]
            else:
                open_orders = self.exchange.privateMixGetV2MixOrderOrdersPending(
                    params={'productType': 'USDT-FUTURES'})['data']['entrustedList']
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'],
                        'order_type': i['orderType'],
                        'amount': i['size'],
                        'price': i['price'],
                        'average_price': i['priceAvg'],
                        'remain_amount': float(i['size']) - float(i['baseVolume']),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        try:
            return self.exchange.privateMixGetV2MixOrderOrdersPending(params={'symbol': symbol.upper() + 'USDT', 'productType': 'USDT-FUTURES'})['data']['entrustedList'][0]['orderId']
        except:
            print(format_exc())
            return None

    # 下单
    def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        """
        # 注意在网页下单页面将持仓模式改为单向持仓
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :param close_position:
        :return:
            {'clientOid': '1210178747881922566', 'orderId': '1210178747881922565'}
        """
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.exchange.privateMixPostV2MixOrderPlaceOrder(params={'symbol': symbol.upper() + 'USDT', 'side': direction, 'orderType': 'limit', 'size': amount, 'price': price, 'productType': 'USDT-FUTURES', 'marginMode': 'crossed', 'marginCoin': 'USDT', 'reduceOnly': 'YES'})[
                        'data']
                else:
                    order_info = self.exchange.privateMixPostV2MixOrderPlaceOrder(params={'symbol': symbol.upper(
                    ) + 'USDT', 'side': direction, 'orderType': 'limit', 'size': amount, 'price': price, 'productType': 'USDT-FUTURES', 'marginMode': 'crossed', 'marginCoin': 'USDT'})['data']
            elif order_type == 'market':
                if close_position:
                    order_info = self.exchange.privateMixPostV2MixOrderPlaceOrder(params={'symbol': symbol.upper(
                    ) + 'USDT', 'side': direction, 'orderType': 'market', 'size': amount, 'productType': 'USDT-FUTURES', 'marginMode': 'crossed', 'marginCoin': 'USDT', 'reduceOnly': 'YES'})['data']
                else:
                    order_info = self.exchange.privateMixPostV2MixOrderPlaceOrder(params={'symbol': symbol.upper(
                    ) + 'USDT', 'side': direction, 'orderType': 'market', 'size': amount, 'productType': 'USDT-FUTURES', 'marginMode': 'crossed', 'marginCoin': 'USDT'})['data']
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['clientOid'],
                'symbol': symbol,
                'direction': direction,
                'amount': amount,
                'price': price if order_type == 'limit' else '',
                'order_type': order_type,
            }
        except:
            print(format_exc())
            return None

    # 查询订单
    def get_swap_order_info(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
            {
              "symbol": "ETHUSDT",
              "size": "0.02",
              "orderId": "1210172298434879489",
              "clientOid": "1210172298434879510",
              "baseVolume": "0",
              "fee": "0",
              "price": "2400",
              "priceAvg": "",
              "state": "live",
              "side": "buy",
              "force": "gtc",
              "totalProfits": "0",
              "posSide": "long",
              "marginCoin": "USDT",
              "presetStopSurplusPrice": "",
              "presetStopLossPrice": "",
              "quoteVolume": "0",
              "orderType": "limit",
              "leverage": "10",
              "marginMode": "crossed",
              "reduceOnly": "NO",
              "enterPointSource": "IOS",
              "tradeSide": "open",
              "posMode": "hedge_mode",
              "orderSource": "normal",
              "newTradeSide": "open",
              "cTime": "1724253480537",
              "uTime": "1724253480567"
            }
        """
        try:
            order_info = self.exchange.privateMixGetV2MixOrderDetail(params={'symbol': symbol.upper(
            ) + 'USDT', 'orderId': order_id, 'productType': 'USDT-FUTURES'})['data']
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': order_info['side'],
                'order_type': order_info['orderType'],
                'amount': order_info['size'],
                'price': order_info['price'],
                'average_price': order_info['priceAvg'],
                'remain_amount': float(order_info['size']) - float(order_info['baseVolume']),
                'fee': abs(order_info['feeDetail']['BGB']['totalDeductionFee'])     # bgb抵扣手续费
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol, order_id):
        """
        :param symbol:
        :param order_id:
        :return:
        {'orderId': '1210172298434879489', 'clientOid': '1210172298434879510'}
        """
        try:
            cancel_order_info = self.exchange.privateMixPostV2MixOrderCancelOrder(
                params={'symbol': symbol.upper() + 'USDT', 'orderId': order_id, 'productType': 'USDT-FUTURES'})['data']
            return {
                'order_id': order_id,
                'symbol': symbol,
                'status': 'canceled' if cancel_order_info['orderId'] else 'failed',
            }
        except:
            print(format_exc())
            return None

    # endregion

    # TODO：币本位合约
    # region =======================================coin swap

    # endregion