# region =======================================备注说明
"""
备注说明：
    官方文档：https://docs.asterdex.com/product/asterex-pro/api/api-document
    官方SDK：https://github.com/asterdex/aster-connector-python

    安装：
    pip install aster-connector-python
    pip install git+https://github.com/asterdex/aster-connector-python.git 

"""
# endregion =======================================备注说明


# region =======================================import
import sys
import os
from traceback import format_exc
from math import log
from typing import Dict, List, Optional, Any, Union
from math import ceil
from time import time
import pandas as pd
from datetime import timedelta

_KZM_FUNCTION_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if _KZM_FUNCTION_DIR not in sys.path:
    sys.path.append(_KZM_FUNCTION_DIR)

from aster.rest_api import Client
from .ExchangeAdapter import ExchangeAdapter
from .api_config import aster_api_config

# endregion =======================================import


class AsterAdapter(ExchangeAdapter):
    BASE_URL = "https://fapi.asterdex.com"  # Aster base URL for fapi

    def __init__(self, account='asterdex-1', proxy_url=None):
        self._api_config = aster_api_config.api_config
        self.account_name = account
        self.exchange_name = 'aster'
        self.client = None
        self.proxy_url = proxy_url

        try:
            if self.account_name in self._api_config:
                account_keys = self._api_config[self.account_name]
                self._api_key = account_keys['api_key']
                self._secret_key = account_keys['secret_key']
                self.client = Client(
                    key=self._api_key,
                    secret=self._secret_key,
                )
                
                # 如果提供了代理URL，设置代理
                if self.proxy_url:
                    self._setup_proxy()
                    
            else:
                print(f"API configuration not found for Aster account: "
                      f"{self.account_name}. Client not initialized.")
        except KeyError:
            print(f"API key or secret key missing in configuration for Aster "
                  f"account: {self.account_name}. Client not initialized.")
            print(format_exc())
        except Exception as e:
            print(f"Error initializing Aster REST Client for account "
                  f"{self.account_name}: {e}")
            print(format_exc())

    def _setup_proxy(self):
        """设置代理"""
        try:
            # 检查client是否有session属性或者可以设置代理
            if hasattr(self.client, '_session'):
                # 如果client有_session属性
                import requests
                session = requests.Session()
                session.proxies = {'https': self.proxy_url, 'http': self.proxy_url}
                self.client._session = session
                print(f"账户 {self.account_name} 代理设置成功: {self.proxy_url}")
            elif hasattr(self.client, 'session'):
                # 如果client有session属性
                import requests
                session = requests.Session()
                session.proxies = {'https': self.proxy_url, 'http': self.proxy_url}
                self.client.session = session
                print(f"账户 {self.account_name} 代理设置成功: {self.proxy_url}")
            else:
                # 尝试通过monkey patch的方式设置代理
                import requests
                original_request = requests.request
                
                def proxied_request(*args, **kwargs):
                    kwargs['proxies'] = {'https': self.proxy_url, 'http': self.proxy_url}
                    return original_request(*args, **kwargs)
                
                # 只对这个实例设置代理
                self._original_request = original_request
                requests.request = proxied_request
                print(f"账户 {self.account_name} 代理设置成功(全局): {self.proxy_url}")
                
        except Exception as e:
            print(f"账户 {self.account_name} 代理设置失败: {e}")
            # 这里不能使用sendSlackMsgAsync，因为可能还没有导入
            print(f"代理设置失败详情: {format_exc()}")

    # region =======================================TODO：spot (Not supported by Aster)
    def get_spot_last_price(self, symbol: str) -> Optional[float]:
        """
        @ param: symbol: str = 'sol'
        @ return:
            {'symbol': 'SOLUSDT', 'price': '176.0900', 'time': *************}
        """
        try:
            return self.client.ticker_price(symbol=f"{symbol.upper()}USDT")
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取买一价
    def get_spot_buy1(self, symbol: str) -> Optional[float]:
        try:
            return float(self.client.ticker_price(symbol=f"{symbol.upper()}USDT")['bidPrice'])
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取卖一价
    def get_spot_sell1(self, symbol: str) -> Optional[float]:
        try:
            return float(self.client.book_ticker(symbol=f"{symbol.upper()}USDT")['askPrice'])
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取买卖一价
    def get_spot_best_orderbook(
        self, symbol: str
    ) -> Optional[Dict[str, List[str]]]:
        try:
            depth_data = self.client.depth(
                symbol=f"{symbol.upper()}USDT", limit=1
            )
            if depth_data and depth_data.get('bids') and len(depth_data['bids']) > 0 and \
               depth_data.get('asks') and len(depth_data['asks']) > 0:
                return {
                    'bid': [depth_data['bids'][0][0], depth_data['bids'][0][1]],
                    'ask': [depth_data['asks'][0][0], depth_data['asks'][0][1]]
                }
            return None
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取买卖盘
    def get_spot_orderbook(
        self, symbol: str, limit: int = 5
    ) -> Optional[Dict[str, Any]]:
        try:
            depth_data = self.client.depth(
                symbol=f"{symbol.upper()}USDT", limit=limit
            )
            if depth_data:
                return {
                    'bids': depth_data.get('bids', []),
                    'asks': depth_data.get('asks', [])
                }
            return None
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取K线
    def get_spot_kline(
        self, symbol: str, period: str = '15m',
        start_time: Optional[int] = None,
        end_time: Optional[int] = None, limit: Optional[int] = None
    ) -> Optional[List[Dict[str, Any]]]:
        return self.get_swap_kline(symbol, period, start_time, end_time, limit)

    # TODO: 获取交易对信息
    def get_spot_instruments_info(
        self, symbol: str
    ) -> Optional[Dict[str, Any]]:
        try:
            exchange_info_data = self.client.exchange_info()
            if exchange_info_data and exchange_info_data.get('symbols'):
                target_symbol_upper = f"{symbol.upper()}USDT"
                for item_details in exchange_info_data['symbols']:
                    if item_details['symbol'] == target_symbol_upper:
                        filters_map = {}
                        for f_item in item_details.get('filters', []):
                            if f_item.get('filterType') == 'PRICE_FILTER':
                                filters_map['price'] = {
                                    'tickSize': f_item.get('tickSize')
                                }
                            elif f_item.get('filterType') == 'LOT_SIZE':
                                filters_map['quantity'] = {
                                    'stepSize': f_item.get('stepSize'),
                                    'minQuantity': f_item.get('minQty')
                                }
                            elif f_item.get('filterType') == 'MIN_NOTIONAL':
                                quantity_filters = filters_map.setdefault(
                                    'quantity', {}
                                )
                                quantity_filters['minNotional'] = f_item.get(
                                    'minNotional'
                                )
                        
                        contract_type_val = item_details.get('contractType')
                        market_type_val = ('PERP'
                                           if contract_type_val == 'PERPETUAL'
                                           else 'SPOT')
                        
                        return {
                            'symbol': item_details['symbol'],
                            'baseSymbol': item_details.get('baseAsset'),
                            'quoteSymbol': item_details.get('quoteAsset'),
                            'filters': filters_map,
                            'marketType': market_type_val,
                            'status': item_details.get('status'),
                            'orderTypes': item_details.get('orderTypes'),
                            'contractType': contract_type_val
                        }
            return None
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取交易对价格最小变动
    def get_spot_order_price_tick_size(self, symbol: str) -> Optional[int]:
        try:
            instrument_info = self.get_spot_instruments_info(symbol)
            price_filter = instrument_info.get('filters', {}).get('price', {})
            if price_filter and price_filter.get('tickSize'):
                tick_size_str = price_filter['tickSize']
                tick_size_float = float(tick_size_str)
                if tick_size_float > 0:
                    return int(round(-log(tick_size_float, 10), 0))
                return 0
            return None
        except Exception:
            print(format_exc())
            return None

    # TODO: 获取交易对数量最小变动
    def get_spot_order_amount_tick_size(self, symbol: str) -> Optional[int]:
        try:
            instrument_info = self.get_spot_instruments_info(symbol)
            qty_filter = instrument_info.get('filters', {}).get('quantity', {})
            if qty_filter and qty_filter.get('stepSize'):
                step_size_str = qty_filter['stepSize']
                step_size_float = float(step_size_str)
                if step_size_float > 0:
                    return int(round(-log(step_size_float, 10), 0))
                return 0
            return None
        except Exception:
            print(format_exc())
            return None

    # TODO: 获取交易对最小数量
    def get_spot_min_amount(self, symbol: str) -> Optional[float]:
        try:
            instrument_info = self.get_spot_instruments_info(symbol)
            qty_filter = instrument_info.get('filters', {}).get('quantity', {})
            if qty_filter and qty_filter.get('minQuantity'):
                min_quantity_str = qty_filter['minQuantity']
                return float(min_quantity_str)
            return None
        except Exception:
            print(format_exc())
            return None

    # TODO: 获取交易对符号
    def get_spot_instruments_symbols(
        self, base_symbol: str = 'usdt'
    ) -> Optional[List[str]]:
        try:
            exchange_info_data = self.client.exchange_info()
            symbols = []
            if exchange_info_data and exchange_info_data.get('symbols'):
                for item in exchange_info_data['symbols']:
                    if item.get('quoteAsset', '').upper() == base_symbol.upper() and \
                       item.get('status') == 'TRADING' and \
                       item.get('contractType') == 'PERPETUAL':
                        symbols.append(item['baseAsset'].lower())
            return symbols
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取交易对单个资产
    def get_spot_account_single_asset(
        self, asset_symbol: str
    ) -> Optional[float]:
        try:
            account_info_data = self.client.account()
            if account_info_data and account_info_data.get('assets'):
                for asset_detail in account_info_data['assets']:
                    if asset_detail['asset'].upper() == asset_symbol.upper():
                        return float(asset_detail.get('availableBalance', 0.0))
            return 0.0
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取交易对所有资产
    def get_spot_account(self) -> Optional[Dict[str, str]]:
        try:
            account_info_data = self.client.account()
            balances = {}
            if account_info_data and account_info_data.get('assets'):
                for asset_detail in account_info_data['assets']:
                    available_bal_str = asset_detail.get('availableBalance', '0')
                    available_bal = float(available_bal_str)
                    if available_bal > 1e-8:
                        balances[asset_detail['asset'].lower()] = available_bal_str
            return balances
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 获取交易对所有订单
    def get_spot_open_order(
        self, symbol: Optional[str] = None
    ) -> Optional[List[Dict[str, Any]]]:
        try:
            open_orders_data = self.client.open_orders(
                symbol=f"{symbol.upper()}USDT" if symbol else None
            )
            formatted_orders = []
            if open_orders_data:
                for item in open_orders_data:
                    item_symbol_upper = item['symbol'].upper()
                    base_s = item_symbol_upper[:-4] \
                        if item_symbol_upper.endswith('USDT') \
                        else item['symbol']
                    
                    remain_amount = str(
                        float(item['origQty']) - float(item['executedQty'])
                    )
                    
                    formatted_orders.append({
                        'order_id': str(item['orderId']),
                        'symbol': base_s.lower(),
                        'direction': ('buy'
                                      if item['side'].upper() == 'BUY'
                                      else 'sell'),
                        'amount': item['origQty'],
                        'price': item['price'],
                        'order_type': item['type'].lower(),
                        'average_price': item.get('avgPrice', ''),
                        'remain_amount': remain_amount,
                        'status': item.get('status', '').lower(),
                        'timestamp': item.get('time')
                    })
            return formatted_orders
        except Exception as e:
            print(format_exc())
            return None

    # TODO: 下单
    def place_spot_order(
        self, symbol: str, direction: str,
        amount: Optional[Union[float, str]] = None,
        price: Optional[Union[float, str]] = None,
        order_type: str = 'limit',
        params=None
    ) -> Optional[Dict[str, Any]]:
        return self.place_swap_order(
            symbol, direction, amount, price, order_type,
            close_position=False, params=params
        )

    # TODO: 获取订单信息
    def get_spot_order_info(
        self, symbol: str, order_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        return self.get_swap_order_info(symbol, order_id)

    # TODO: 取消订单
    def cancel_spot_order(
        self, symbol: str, order_id: str
    ) -> Optional[Dict[str, Any]]:
        return self.cancel_swap_order(symbol, order_id)
    # endregion

    # region =======================================TODO: future (Future for Aster)
    def get_instrument_id(self, symbol, margin_type='usdt', contract_type=''):
        if margin_type.lower() == 'usdt':
            return f"{symbol.upper()}USDT"
        return None

    def get_future_buy1(self, symbol, margin_type='usdt', contract_type='quarter'):
        return self.get_swap_buy1(symbol)

    def get_future_sell1(self, symbol, margin_type='usdt', contract_type='quarter'):
        return self.get_swap_sell1(symbol)

    def get_future_account(self, symbol, margin_type='usdt'):
        if margin_type.lower() == 'usdt':
            return self.get_swap_account_single_asset(symbol.upper())
        return None

    def get_future_position(self, symbol, margin_type='usdt'):
        return self.get_swap_position(symbol)

    def get_future_open_order(self, symbol, margin_type='usdt'):
        return self.get_swap_open_order(symbol)

    def get_future_open_order_id(self, symbol, margin_type):
        open_orders = self.get_future_open_order(symbol, margin_type)
        if open_orders and len(open_orders) > 0:
            return open_orders[0].get('order_id')
        return None

    def get_future_order_info(
        self, order_id: str, instrument_id: str = ''
    ) -> Optional[Dict[str, Any]]:
        if not instrument_id:
            return None
        base_symbol = (instrument_id[:-4]
                       if instrument_id.upper().endswith("USDT")
                       else instrument_id)
        return self.get_swap_order_info(symbol=base_symbol, order_id=order_id)

    def get_future_avail_amount(
        self, symbol: str, margin_type: str = 'usdt'
    ) -> float:
        positions = self.get_swap_position(symbol)
        if positions and isinstance(positions, list) and len(positions) > 0:
            pos_data = positions[0]
            return float(pos_data.get('amount', 0.0))
        return 0.0

    def place_future_order(
        self, symbol: str, amount: Union[float, str], direction: str,
        order_type: str = 'limit', margin_type: str = 'usdt',
        contract_type: str = 'quarter', price: str = ''
    ):
        close_position = False
        actual_direction = direction.lower()
        if actual_direction == 'close_buy':
            actual_direction = 'sell'
            close_position = True
        elif actual_direction == 'close_sell':
            actual_direction = 'buy'
            close_position = True
        
        return self.place_swap_order(
            symbol=symbol, direction=actual_direction, amount=str(amount),
            price=price, order_type=order_type,
            close_position=close_position
        )

    def cancel_future_order(
        self, order_id: str, instrument_id: str
    ) -> Optional[Dict[str, Any]]:
        base_symbol = (instrument_id[:-4]
                       if instrument_id.upper().endswith("USDT")
                       else instrument_id)
        return self.cancel_swap_order(symbol=base_symbol, order_id=order_id)
    # endregion

    # region =======================================usdt swap (Perpetuals for Aster)
    # 获取最新价格
    def get_swap_latest_price(self, symbol: str) -> Optional[float]:
        try:
            return float(self.client.ticker_price(symbol=f"{symbol.upper()}USDT")['price'])
        except Exception as e:
            print(format_exc())
            return None

    # 获取买一价
    def get_swap_buy1(self, symbol: str) -> Optional[float]:
        try:
            return float(self.client.book_ticker(symbol=f"{symbol.upper()}USDT")['bidPrice'])
        except:
            print(format_exc())
            return None

    # 获取卖一价
    def get_swap_sell1(self, symbol: str) -> Optional[float]:
        try:
            return float(self.client.book_ticker(symbol=f"{symbol.upper()}USDT")['askPrice'])
        except:
            print(format_exc())
            return None

    # 获取买卖一价
    def get_swap_best_orderbook(self, symbol: str) -> Optional[Dict[str, List[str]]]:
        try:
            info = self.client.book_ticker(symbol=f"{symbol.upper()}USDT")
            return {
                'bid': [info['bidPrice'], info['bidQty']],
                'ask': [info['askPrice'], info['askQty']]
            }
        except:
            print(format_exc())
            return None

    # 获取买卖盘
    def get_swap_orderbook(self, symbol: str, limit: int = 5) -> Optional[Dict[str, Any]]:
        """
        @ symbol: str
        @ limit: int
        @ return:
        {
            lastUpdateId: 147330924387,
            E: 1747895326240,
            T: 1747895326229,
            bids: [
                ["110846.7", "0.044"],
                ["110846.6", "0.200"],
                ["110846.3", "0.018"],
                ["110846.2", "0.027"],
                ["110845.9", "2.822"]
            ],
            asks: [
                ["110874.7", "0.824"],
                ["110874.8", "0.010"],
                ["110874.9", "1.412"],
                ["110875.0", "0.001"],
                ["110876.2", "1.414"]
            ]
            }
        """
        try:
            info = self.client.depth(symbol=f"{symbol.upper()}USDT", limit=limit)
            return {
                'bids': info['bids'],
                'asks': info['asks']
            }
        except:
            print(format_exc())
            return None

    # 获取合约资金费率
    def get_swap_contract_funding_rate(self, symbol: str) -> Optional[float]:
        try:
            return float(self.client.mark_price(symbol=f"{symbol.upper()}USDT")['lastFundingRate'])
        except:
            print(format_exc())
            return None

    # 获取合约资金费率历史
    def get_swap_contract_history_funding_rate(self, symbol: str, limit: int = 5) -> Optional[List[Dict[str, Any]]]:
        try:
            funding_rates = self.client.funding_rate(symbol=f"{symbol.upper()}USDT", limit=limit)
            return {
                'symbol': symbol,
                'funding_rate': [i['fundingRate'] for i in funding_rates],
                'funding_time': [i['fundingTime'] for i in funding_rates]
            }
        except:
            print(format_exc())
            return None

    # TODO: 获取合约持仓量
    def get_swap_contract_open_interest(self, symbol: str) -> Optional[float]:
        pass

    # TODO: 获取K线
    def get_swap_kline(self, symbol: str, period: str = '15m', start_time: Optional[int] = None, end_time: Optional[int] = None, limit: Optional[int] = None) -> Optional[List[Dict[str, Any]]]:
        pass

    # 获取交易对信息(TODO:暂未统一格式)
    def get_swap_instruments_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        @ symbol: str
        @ return:
        {
            symbol: "BTCUSDT",
            pair: "BTCUSDT",
            contractType: "PERPETUAL",
            deliveryDate: 4133404800000,
            onboardDate: 1627628400000,
            status: "TRADING",
            maintMarginPercent: "2.5000",
            requiredMarginPercent: "5.0000",
            baseAsset: "BTC",
            quoteAsset: "USDT",
            marginAsset: "USDT",
            pricePrecision: 1,
            quantityPrecision: 3,
            baseAssetPrecision: 8,
            quotePrecision: 8,
            underlyingType: "COIN",
            underlyingSubType: [],
            settlePlan: 0,
            triggerProtect: "0.0200",
            liquidationFee: "0.025000",
            marketTakeBound: "0.02",
            filters: [
                {
                minPrice: "1",
                maxPrice: "1000000",
                filterType: "PRICE_FILTER",
                tickSize: "0.1"
                },
                {
                stepSize: "0.001",
                filterType: "LOT_SIZE",
                maxQty: "100",
                minQty: "0.001"
                },
                {
                stepSize: "0.001",
                filterType: "MARKET_LOT_SIZE",
                maxQty: "4",
                minQty: "0.001"
                },
                { limit: 200, filterType: "MAX_NUM_ORDERS" },
                { limit: 10, filterType: "MAX_NUM_ALGO_ORDERS" },
                { notional: "5", filterType: "MIN_NOTIONAL" },
                {
                multiplierDown: "0.9800",
                multiplierUp: "1.0200",
                multiplierDecimal: "4",
                filterType: "PERCENT_PRICE"
                }
            ],
            orderTypes: [
                "LIMIT",
                "MARKET",
                "STOP",
                "STOP_MARKET",
                "TAKE_PROFIT",
                "TAKE_PROFIT_MARKET",
                "TRAILING_STOP_MARKET"
            ],
            timeInForce: ["GTC", "IOC", "FOK", "GTX", "RPI"]
        }
        """
        try:
            for i in self.client.exchange_info()['symbols']:
                if i['symbol'] == f"{symbol.upper()}USDT":
                    return i
            return None
        except:
            print(format_exc())
            return None

    # 获取交易对价格最小变动
    def get_swap_order_price_tick_size(self, symbol: str) -> Optional[int]:
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['filters'][0]['tickSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取交易对数量最小变动
    def get_swap_order_amount_tick_size(self, symbol: str) -> Optional[int]:
        try:
            return int(round(log(1 / float(self.get_swap_instruments_info(symbol)['filters'][1]['stepSize']), 10), 0))
        except:
            print(format_exc())
            return None

    # 获取交易对最小数量
    def get_swap_min_amount(self, symbol: str) -> Optional[float]:
        try:
            info = self.get_swap_instruments_info(symbol)['filters']
            _buy1 = float(self.client.book_ticker(symbol=f"{symbol.upper()}USDT")['bidPrice'])
            _amount_precision = int(round(log(1 / float(info[1]['stepSize']), 10), 0))
            # 向上取数，小数点后保留_amount_precision位
            return ceil((float(info[5]['notional'])/ _buy1) * (10 ** _amount_precision)) / (10 ** _amount_precision)
        except:
            print(format_exc())
            return None

    # 获取交易对符号
    def get_swap_instruments_symbols(self, base_symbol: str = 'USDT') -> Optional[List[str]]:
        try:
            return [i['symbol'] for i in self.client.exchange_info()['symbols'] if all([i['quoteAsset'] == base_symbol.upper(), i['status'] == 'TRADING'])]
        except:
            print(format_exc())
            return None

    # 获取永续合约账户信息(单一币种,默认usdt)
    def get_swap_account_single_asset(self, symbol: str = 'USDT') -> Optional[float]:
        """
        @ param: symbol: str = 'USDT'
        @ return:
            [
                {
                    accountAlias: "FzfWAuAuoCfWAu",
                    asset: "APX",
                    balance: "0.********",
                    crossWalletBalance: "0.********",
                    crossUnPnl: "0.********",
                    availableBalance: "0.********",
                    maxWithdrawAmount: "0.********",
                    marginAvailable: False,
                    updateTime: 0
                },
                {
                    accountAlias: "FzfWAuAuoCfWAu",
                    asset: "FBTC",
                    balance: "0.********",
                    crossWalletBalance: "0.********",
                    crossUnPnl: "0.********",
                    availableBalance: "0.********",
                    maxWithdrawAmount: "0.********",
                    marginAvailable: True,
                    updateTime: 0
                },
                ...
            ]
        """
        try:
            info = self.client.balance(asset=symbol.upper())
            if info:
                for i in info:
                    if i['asset'] == symbol.upper():
                        return float(i['availableBalance'])
            return None
        except:
            print(format_exc())
            return None

    # 获取保证金率
    def get_swap_margin_rate(self) -> Optional[float]:
        try:
            _info = self.client.account()
            _margin_rate = float(_info['totalMaintMargin']) / float(_info['availableBalance'])
            return _margin_rate
        except:
            print(format_exc())
            return None

    # 获取合约持仓
    def get_swap_position(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        @ param: symbol: str = 'eth'
        @ return:
            [
                {
                    symbol: "DOGEUSDT",
                    positionAmt: "30",
                    entryPrice: "0.24794",
                    markPrice: "0.********",
                    unRealizedProfit: "-0.********",
                    liquidationPrice: "0",
                    leverage: "20",
                    maxNotionalValue: "5000",
                    marginType: "cross",
                    isolatedMargin: "0.********",
                    isAutoAddMargin: "false",
                    positionSide: "BOTH",
                    notional: "7.********",
                    isolatedWallet: "0",
                    updateTime: 1747980244531
                }
            ]
        """
        try:
            if symbol:
                info = self.client.get_position_risk(symbol=f"{symbol.upper()}USDT")
                if info:
                    info = info[0]
                    if float(info['positionAmt']) == 0:
                        return []
                    else:
                        return [
                        {
                            'symbol': symbol,
                            'direction': 'buy' if float(info['positionAmt']) > 0 else 'sell',
                            'amount': abs(float(info['positionAmt'])),
                            'price': info['entryPrice'],
                            'liquidation_price': info['liquidationPrice'],
                            'unrealized_pnl': float(info['unRealizedProfit']),      # 其他交易所Adapter中没有这个字段
                        }
                    ]
                else:
                    return []
            else:
                info = self.client.get_position_risk()
                if info:
                    return [
                        {
                            'symbol': i['symbol'].replace('USDT', '').lower(),
                            'direction': 'buy' if float(i['positionAmt']) > 0 else 'sell',
                            'amount': abs(float(i['positionAmt'])),
                            'price': i['entryPrice'],
                            'liquidation_price': i['liquidationPrice'],
                            'unrealized_pnl': float(i['unRealizedProfit']),      # 其他交易所Adapter中没有这个字段
                        } for i in info if float(i['positionAmt']) != 0
                    ]
                else:
                    return []
        except:
            print(format_exc())
            return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        @ param: symbol: str = 'eth'
        @ return:
            [
                {
                    orderId: 1166133462,
                    symbol: "ADAUSDT",
                    status: "NEW",
                    clientOrderId: "web_AD_ususdtsz5jzqv2tmc_x",
                    price: "0.85000",
                    avgPrice: "0",
                    origQty: "20",
                    executedQty: "0",
                    cumQuote: "0",
                    timeInForce: "GTC",
                    type: "LIMIT",
                    reduceOnly: False,
                    closePosition: False,
                    side: "SELL",
                    positionSide: "BOTH",
                    stopPrice: "0",
                    workingType: "CONTRACT_PRICE",
                    priceProtect: False,
                    origType: "LIMIT",
                    time: 1747981610716,
                    updateTime: 1747981610716
                }
            ]
        """
        try:
            if symbol:
                open_orders = self.client.get_orders(symbol=f"{symbol.upper()}USDT")
                if open_orders:
                    return [
                        {
                            'order_id': i['orderId'],
                            'symbol': symbol,
                            'direction': i['side'].lower(),
                            'order_type': i['type'].lower(),
                            'amount': i['origQty'],
                            'price': i['price'],
                            'average_price': i['avgPrice'],
                            'remain_amount': float(i['origQty']) - float(i['executedQty']),
                        } for i in open_orders
                    ]
            else:
                open_orders = self.client.get_orders()
                return [
                    {
                        'order_id': i['orderId'],
                        'symbol': i['symbol'].replace('USDT', '').lower(),
                        'direction': i['side'].lower(),
                        'order_type': i['type'].lower(),
                        'amount': i['origQty'],
                        'price': i['price'],
                        'average_price': i['avgPrice'],
                        'remain_amount': float(i['origQty']) - float(i['executedQty']),
                    } for i in open_orders
                ]
        except:
            print(format_exc())
            return None

    # 获取合约订单ID
    def get_swap_open_order_id(self, symbol: str) -> Optional[str]:
        open_orders = self.get_swap_open_order(symbol=symbol)
        if open_orders and len(open_orders) > 0:
            return open_orders[0].get('order_id')
        return None

    # 下单
    def place_swap_order(self, symbol: str, direction: str, amount: Union[float, str], price: Optional[Union[float, str]] = None, order_type: str = 'limit', close_position: Optional[bool] = False, params=None) -> Optional[Dict[str, Any]]:
        try:
            if order_type == 'limit':
                if close_position:
                    order_info = self.client.new_order(symbol=f"{symbol.upper()}USDT", side=direction.upper(), type='LIMIT', quantity=amount, price=price, timeInForce='GTC', reduceOnly='true')
                else:
                    order_info = self.client.new_order(symbol=f"{symbol.upper()}USDT", side=direction.upper(), type='LIMIT', quantity=amount, price=price, timeInForce='GTC')
            elif order_type == 'market':
                # 自动市价平仓
                if close_position:
                    order_info = self.client.new_order(symbol=f"{symbol.upper()}USDT", side=direction.upper(), type='MARKET', quantity=amount, reduceOnly='true')
                else:
                    order_info = self.client.new_order(symbol=f"{symbol.upper()}USDT", side=direction.upper(), type='MARKET', quantity=amount)
            else:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_info['orderId'],
                'symbol': symbol,
                'direction': direction,
                'amount': order_info['origQty'],
                'price': order_info['price'],
                'order_type': order_type,
                'client_order_id': order_info['clientOrderId'],     # 新增client_order_id字段，其他交易所暂时未添加该字段
            }
        except:
            print(format_exc())
            return None

    # 获取订单信息
    def get_swap_order_info(self, symbol: str, order_id: str, client_order_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        @ param: symbol: str = 'eth'
        @ param: order_id: str = '1166133462'
        @ return:
            {
                orderId: **********,
                symbol: "DOGEUSDT",
                status: "CANCELED",
                clientOrderId: "web_AD_fzh7fuptw5szmnlm4_x",
                price: "0.210000",
                avgPrice: "0.000000",
                origQty: "25",
                executedQty: "0",
                cumQuote: "0",
                timeInForce: "GTC",
                type: "LIMIT",
                reduceOnly: False,
                closePosition: False,
                side: "BUY",
                positionSide: "BOTH",
                stopPrice: "0",
                workingType: "CONTRACT_PRICE",
                priceProtect: False,
                origType: "LIMIT",
                time: 1747984133879,
                updateTime: 1747984711521
                }
        """
        try:
            if order_id:
                info = self.client.query_order(symbol=f"{symbol.upper()}USDT", orderId=order_id)
            elif client_order_id:
                info = self.client.query_order(symbol=f"{symbol.upper()}USDT", clientOrderId=client_order_id)
            else:
                return None
            if not info:
                return None
            return {
                'exchange': self.exchange_name,
                'order_id': order_id if order_id else client_order_id,
                'symbol': symbol,
                'direction': info['side'].lower(),
                'order_type': info['type'].lower(),
                'amount': info['origQty'],
                'price': info['price'],
                'average_price': info['avgPrice'],
                'remain_amount': float(info['origQty']) - float(info['executedQty']),
                'fee': None,
            }
        except:
            print(format_exc())
            return None

    # 取消订单
    def cancel_swap_order(self, symbol: str, order_id:Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        @ param: symbol: str = 'eth'
        @ param: order_id: str = '1166133462'
        @ return:
            {
                orderId: 540967343,
                symbol: "SUIUSDT",
                status: "CANCELED",
                clientOrderId: "LjEYk2nHIsiRIUTHgIlXLU",
                price: "3.415000",
                avgPrice: "0.0000000",
                origQty: "6.5",
                executedQty: "0",
                cumQty: "0",
                cumQuote: "0",
                timeInForce: "GTC",
                type: "LIMIT",
                reduceOnly: False,
                closePosition: False,
                side: "BUY",
                positionSide: "BOTH",
                stopPrice: "0",
                workingType: "CONTRACT_PRICE",
                priceProtect: False,
                origType: "LIMIT",
                updateTime: 1748309068070
                }
        """
        try:
            if order_id:
                cancel_order_info = self.client.cancel_order(symbol=f"{symbol.upper()}USDT", orderId=order_id)
                if cancel_order_info['orderId']:
                    return {
                        'order_id': order_id,
                        'symbol': symbol,
                        'status': 'canceled' if cancel_order_info['status'] == 'CANCELED' else 'failed',
                    }
            else:
                cancel_order_info = self.client.cancel_open_orders(symbol=f"{symbol.upper()}USDT")
                if cancel_order_info:
                    return {
                        'order_id': None,
                        'symbol': symbol,
                        # TODO：有疑问，未测试
                        'status': 'canceled' if cancel_order_info['msg'] == 'The operation of cancel all open order is done.' else 'failed',
                    }
        except:
            print(format_exc())
            return None

    # 获取下单记录（注意：获取到的是下单明细）
    def fetch_my_swap_orders(self, symbol, date=1, direction='buy'):
        """
        :param symbol:
        :param margin_type:
        :return:
            [
                {
                    symbol: "SUIUSDT",
                    id: 10449448,
                    orderId: 551699744,
                    side: "BUY",
                    price: "3.274100",
                    qty: "7",
                    realizedPnl: "0",
                    marginAsset: "USDT",
                    quoteQty: "22.9187000",
                    commission: "0.00229187",
                    commissionAsset: "USDT",
                    time: 1749011261848,
                    positionSide: "BOTH",
                    maker: True,
                    buyer: True
                },
                {
                    symbol: "SUIUSDT",
                    id: 10449452,
                    orderId: 551701357,
                    side: "SELL",
                    price: "3.268000",
                    qty: "3.5",
                    realizedPnl: "-0.********",
                    marginAsset: "USDT",
                    quoteQty: "11.4380000",
                    commission: "0.********",
                    commissionAsset: "USDT",
                    time: *************,
                    positionSide: "BOTH",
                    maker: False,
                    buyer: False
                }...
            ]
        """
        days_ago = int(time() * 1000) - date * 24 * 60 * 60 * 1000
        info = self.client.get_account_trades(symbol=f"{symbol.upper()}USDT", limit=1000)
        df = pd.DataFrame(info)
        if info[0]['time'] > days_ago:
            while True:
                end_time = info[0]['time']
                info = self.client.get_account_trades(symbol=f"{symbol.upper()}USDT", limit=1000, endTime=end_time)
                if info[0]['time'] == df.iloc[-1]['time']:
                    break
                df = pd.concat([df, pd.DataFrame(info)], axis=0)
                if info[0]['time'] < days_ago:
                    break
        df['datetime'] = pd.to_datetime(pd.to_numeric(df['time']), unit='ms') + timedelta(hours=8)
        df = df[df['side'] == direction.upper()]
        df.sort_values(by='datetime', ascending=False, inplace=True)
        df.drop_duplicates(subset=['id'], keep='first', inplace=True)
        # 截取最近两天时间的数据
        df = df[df['datetime'] > pd.to_datetime(days_ago, unit='ms')]
        df.reset_index(drop=True, inplace=True)
        return df

    # endregion

    # region =======================================TODO：coin swap (Not supported by Aster fapi)
    def get_coin_swap_latest_price(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_buy1(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_sell1(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_best_orderbook(self, symbol: str) -> Optional[Dict[str, List[str]]]: pass
    def get_coin_swap_orderbook(self, symbol: str, limit: int = 5) -> Optional[Dict[str, Any]]: pass
    def get_coin_swap_contract_funding_rate(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_contract_history_funding_rate(self, symbol: str, limit: int = 5) -> Optional[List[Dict[str, Any]]]: pass
    def get_coin_swap_contract_open_interest(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_kline(self, symbol: str, period: str = '15m', start_time: Optional[int] = None, end_time: Optional[int] = None, limit: Optional[int] = None) -> Optional[List[Dict[str, Any]]]: pass
    def get_coin_swap_instruments_info(self, symbol: str) -> Optional[Dict[str, Any]]: pass
    def get_coin_swap_order_price_tick_size(self, symbol: str) -> Optional[int]: pass
    def get_coin_swap_order_amount_tick_size(self, symbol: str) -> Optional[int]: pass
    def get_coin_swap_min_amount(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_instruments_symbols(self) -> Optional[List[str]]: pass
    def get_coin_swap_account_single_asset(self, symbol: str) -> Optional[float]: pass
    def get_coin_swap_margin_rate(self) -> Optional[float]: pass
    def get_coin_swap_position(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]: pass
    def get_coin_swap_open_order(self, symbol: Optional[str] = None) -> Optional[List[Dict[str, Any]]]: pass
    def get_coin_swap_open_order_id(self, symbol: str) -> Optional[str]: pass
    def place_coin_swap_order(self, symbol: str, direction: str, amount: Union[float, str], price: Optional[Union[float, str]] = None, order_type: str = 'limit', close_position: Optional[bool] = False) -> Optional[Dict[str, Any]]: pass
    def get_coin_swap_order_info(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]: pass
    def cancel_coin_swap_order(self, symbol: str, order_id: str) -> Optional[Dict[str, Any]]: pass
    # endregion


# a = AsterAdapter()
# b = a.get_swap_margin_rate()
# # # b = a.cancel_swap_order(symbol='doge', order_id='**********')
# # # b = a.get_swap_position('doge')
# # b = a.get_swap_order_info('doge', '**********')
# # # b = a.place_swap_order('sol', 'buy', 0.03, order_type='market', close_position=True)
# print(b)

# # --- Test this proxy ---
# proxy_url = '******************************************' # For asterdex-1
# # proxy_url = 'socks5://hFTtZ5:zyEtKo@91.212.120.123:8000' # For asterdex-2 (Example)
# # ... and so on for other proxies

# proxies = {
#     'http': proxy_url,
#     'https': proxy_url,
# }

# # AsterDEX fAPI exchangeInfo endpoint
# exchange_info_url = 'https://fapi.asterdex.com/fapi/v1/exchangeInfo' # Verify this is the correct base for the SDK's call

# print(f"Testing proxy: {proxy_url} against {exchange_info_url}")

# try:
#     # Add a timeout to prevent indefinite hanging
#     response = requests.get(exchange_info_url, proxies=proxies, timeout=20) # 20-second timeout
#     response.raise_for_status() # Raise an exception for HTTP errors (4xx or 5xx)
#     data = response.json()
#     print(f"SUCCESS with proxy {proxy_url}!")
#     print(f"Data (first 500 chars): {str(data)[:500]}")
# except requests.exceptions.Timeout:
#     print(f"FAILED with proxy {proxy_url}: Request timed out.")
# except requests.exceptions.ProxyError as e:
#     print(f"FAILED with proxy {proxy_url}: Proxy error: {e}")
# except requests.exceptions.SSLError as e:
#     print(f"FAILED with proxy {proxy_url}: SSL error: {e}")
# except requests.exceptions.RequestException as e:
#     print(f"FAILED with proxy {proxy_url}: {e}")