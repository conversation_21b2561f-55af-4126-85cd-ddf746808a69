# region ============================================备注说明
"""
备注说明：
https://github.com/hyperliquid-dex/hyperliquid-python-sdk
原版本安装代码：
pip install hyperliquid-python-sdk==0.9
目前版本：0.9
    新版本安装代码：
    pip install git+https://github.com/kewell2000/hyperliquid-python-sdk.git
    目前版本：0.9
    修改了部分接口，添加了get_swap_contract_at_open_interest_cap接口，用于获取持仓限制名单
"""
# endregion ============================================备注说明


# region ============================================import
import sys
from os.path import abspath, dirname
sys.path.insert(0, dirname(dirname(abspath(__file__))))
import os
import pandas as pd

# 根据操作系统设置正确的路径
if os.path.exists('/Volumes/Cryptomator'):  # macOS
    sys.path.append('/Volumes/Cryptomator/quant/pycharmproject/ssh/kzmfunction/')
else:  # Ubuntu服务器
    sys.path.append('/home/<USER>/ssh/kzmfunction/')

from datetime import timedelta, datetime
from .ExchangeAdapter import ExchangeAdapter
from ..api_config import hyperliquid_api_config
from math import ceil, log
from time import sleep
from traceback import format_exc

# endregion ============================================import


class HyperliquidAdapter(ExchangeAdapter):
    def __init__(self, account='hyperliquid-持仓账号', environment='mainnet'):
        import eth_account
        from hyperliquid.exchange import Exchange
        from hyperliquid.utils import constants
        self._api_config = hyperliquid_api_config.api_config
        self.account = account
        self.exchange_name = 'hyperliquid'

        # # 创建public交易所
        account_key = eth_account.Account.from_key(
            self._api_config[account]['ethereum_address_key']
            )
        self.exchange = Exchange(
            account_key, 
            constants.MAINNET_API_URL if environment == 'mainnet' else constants.TESTNET_API_URL, 
            account_address=self._api_config[account]['ethereum_address']
            )
        # approve_result, agent_key = self.exchange.approve_agent()
        self.ethereum_address = self._api_config[account]['ethereum_address']
        self.limit_fee = '0.0015'
        
# region =======================================================spot
    # FIXME: 注意：hyperliquid上的现货目前:
    # btc、eth、sol需要改为ubtc、ueth、usol
    # fartcoin改为ufart

    # 获取spot symbol id
    def get_spot_symbol_id(self, symbol):
        """
        [
            "purrusdc",
            "hfunusdc",
            "lickusdc",
            "manletusdc",
            "jeffusdc",
            "sixusdc",
            "wagmiusdc",
            "cappyusdc",
            "pointsusdc",
            "trumpusdc",
            "gmeowusdc",
            "pepeusdc",
            "xulianusdc",
            "rugusdc",
            "iliensusdc",
            "fuckyusdc",
            "czusdc",
            "bagsusdc",
            "ansemusdc",
            "tateusdc",
            "funusdc",
            "suckyusdc",
            "bigbenusdc",
            "kobeusdc",
            "vegasusdc",
            "pumpusdc",
            "schizousdc",
            "catnipusdc",
            "happyusdc",
            "sellusdc",
            "hboostusdc",
            "farmedusdc",
            "purrpsusdc",
            "gupusdc",
            "gptusdc",
            "pandausdc",
            "bidusdc",
            "hodlusdc",
            "rageusdc",
            "asiusdc",
            "vaporusdc",
            "pillusdc",
            "adhdusdc",
            "ladyusdc",
            "catusdc",
            "hpepeusdc",
            "mbappeusdc",
            "magausdc",
            "mogusdc",
            "omnixusdc",
            "jeetusdc",
            "dropusdc",
            "ariusdc",
            "testusdc",
            "meowusdc",
            "antusdc",
            "fracusdc",
            "atehunusdc",
            "frctusdc",
            "cozyusdc",
            "washusdc",
            "nftusdc",
            "pearusdc",
            "richusdc",
            "guessusdc",
            "lorausdc",
            "catbalusdc",
            "tjifusdc",
            "neirousdc",
            "timeusdc",
            "berausdc",
            "maxiusdc",
            "nmtdusdc",
            "hpumpusdc",
            "pigeonusdc",
            "launchusdc",
            "riseusdc",
            "wowusdc",
            "cindyusdc",
            "chinausdc",
            "stackusdc",
            "friedusdc",
            "picklusdc",
            "shrekusdc",
            "nocexusdc",
            "vaultusdc",
            "rankusdc",
            "husdc",
            "gusdc",
            "monadusdc",
            "bozousdc",
            "ripusdc",
            "upusdc",
            "sphusdc",
            "hopeusdc",
            "shoeusdc",
            "bussyusdc",
            "fatcatusdc",
            "pipusdc",
            "yeetiusdc",
            "lqnausdc",
            "nasdaqusdc",
            "sylviusdc",
            "feitusdc",
            "strictusdc",
            "frudousdc",
            "viznusdc",
            "autistusdc",
            "hgodusdc",
            "liquidusdc",
            "chefusdc",
            "earthusdc",
            "niggousdc",
            "luckyusdc",
            "hopusdc",
            "munchusdc",
            "copeusdc",
            "hpyhusdc",
            "yapusdc",
            "hypeusdc",
            "steelusdc",
            "retardusdc",
            "holdusdc",
            "starusdc",
            "genesyusdc",
            "solvusdc",
            "bubzusdc",
            "sheepusdc",
            "farmusdc",
            "flaskusdc",
            "sovrnusdc",
            "monusdc",
            "godusdc",
            "hyenausdc",
            "depinusdc",
            "beatsusdc",
            "orausdc",
            "liqdusdc",
            "sentusdc",
            "flyusdc",
            "hwtrusdc",
            "pegusdc",
            "jpegusdc",
            "ubtcusdc",
            "headusdc",
            "prfiusdc",
            "vortxusdc",
            "definusdc",
            "tiltusdc",
            "whypiusdc",
            "anonusdc",
            "ottiusdc",
            "uethusdc",
            "buddyusdc",
            "fundusdc",
            "usdeusdc",
            "usdxlusdc",
            "ushusdc",
            "feusdusdc",
            "cookusdc",
            "usrusdc",
            "quantusdc",
            "ratusdc",
            "usolusdc",
            "purrousdc",
            "trendusdc",
            "usdt0usdc",
            "ufartusdc",
            "diablousdc",
            "penisusdc",
            "rubusdc",
            "perpusdc"
        ]
        """
        for _ in range(3):
            try:
                _info = self.exchange.info.spot_meta()
                for i in _info['tokens']:
                    if i['name'] == symbol.upper():
                        index = i['index']
                        for j in _info['universe']:
                            if j['tokens'][0] == index:
                                return j['name']
                return None
            except Exception as e:
                print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # TODO：获取最新价格
    def get_spot_last_price(self, symbol):
        """
        返回当前spot最新成交价格
        @param symbol:      eth
        @return:
        """
        return self.exchange.info.spot_meta()['tokens']

    # 获取spot盘口买1价
    def get_spot_buy1(self, symbol):
        """
        返回当前spot盘口的买1价格
        @param symbol:      btc
        @return:
        """
        for _ in range(3):
            try:
                return float(self.exchange.info.l2_snapshot(symbol.upper() + '/USDC')['levels'][0][0]['px'])
            except Exception as e:
                print(format_exc())
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取spot盘口卖1价
    def get_spot_sell1(self, symbol):
        """
        返回当前spot盘口的卖1价格
        @param symbol:      btc
        @return:
        """
        for _ in range(3):
            try:
                return float(self.exchange.info.l2_snapshot(symbol.upper() + '/USDC')['levels'][1][0]['px'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def get_spot_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60972.0', '0.85664'], 'ask': ['60973.0', '0.65602']}
        """
        for _ in range(3):
            try:
                raw_orderbook = self.exchange.info.l2_snapshot(symbol.upper() + '/USDC')[
                    'levels']
                return {
                    'bid': [raw_orderbook[0][0]['px'], raw_orderbook[0][0]['sz']],
                    'ask': [raw_orderbook[1][0]['px'], raw_orderbook[1][0]['sz']]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def get_spot_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              "60985.0",
              "0.42524"
            ],
            [
              "60984.0",
              "0.17214"
            ],
            ...
          ],
          "asks": [
            [
              "60986.0",
              "2.16659"
            ],
            [
              "60987.0",
              "0.96215"
            ],
            ...
          ]
        }
        """
        for _ in range(3):
            try:
                raw_orderbook = self.exchange.info.l2_snapshot(symbol.upper() + '/USDC')[
                    'levels']
                return {
                    'symbol': symbol,
                    'bids': [[i['px'], i['sz']] for i in raw_orderbook[0][:limit]],
                    'asks': [[i['px'], i['sz']] for i in raw_orderbook[1][:limit]]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # # TODO: 获取spot-K线
    def get_spot_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_spot_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
            {
                name: "UBTC",
                szDecimals: 5,
                weiDecimals: 10,
                index: 197,
                tokenId: "0x8f254b963e8468305d409b33aa137c67",
                isCanonical: False,
                evmContract: {
                    address: "******************************************",
                    evm_extra_wei_decimals: -2
                },
                fullName: "Unit Bitcoin",
                deployerTradingFeeShare: "1.0"
            }
        """
        for _ in range(3):
            try:
                # _info = self.exchange.info.spot_meta()['universe']
                _info = self.exchange.info.spot_meta()['tokens']
                for i in _info:
                    if i['name'] == symbol.upper():
                        return i
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取下单价格精度
    def get_spot_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                symbol_buy1 = self.get_spot_buy1(symbol)
                symbol_sell1 = self.get_spot_sell1(symbol)
                if len(str(symbol_buy1)) == len(str(symbol_sell1)):
                    return len(str(symbol_buy1).split('.')[1]) if int(str(symbol_buy1).split('.')[1]) > 0 else 0
                else:
                    return max(len(str(symbol_buy1).split('.')[1]) if int(str(symbol_buy1).split('.')[1]) > 0 else 0, len(str(symbol_sell1).split('.')[1]) if int(str(symbol_sell1).split('.')[1]) > 0 else 0)
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取下单数量精度
    def get_spot_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        return self.get_spot_instruments_info(symbol)['szDecimals']

    # 获取最小下单数量
    def get_spot_min_amount(self, symbol):
        """
        最小下单金额10u
        获取最小下单数量
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                _price = self.get_spot_buy1(symbol)
                _amount_precision = self.get_spot_order_amount_tick_size(
                    symbol)
                return ceil(10 / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            except Exception as e:
                if '429' in str(e):
                    sleep(5)
                print('当前获取数据遇到429错误,等待5秒后继续...')
        return None

    # 获取现货交易对(hyperliquid只有usdc交易对)
    def get_spot_instruments_symbols(self, base_symbol='usdc'):
        """
        添加usdt是为了后续和其他交易所做比对，实际应该为usdc合约
        :return:
        """
        for _ in range(3):
            try:
                _symbols_list = []
                coins = [i['name'] for i in self.exchange.info.spot_meta()['tokens']]
                coins.remove('USDC')
                for i in coins:
                    try:
                        _info = self.exchange.info.l2_snapshot(
                            i + '/USDC')['levels']
                        if _info[0]:
                            _symbols_list.append(i.lower() + base_symbol)
                    except:
                        continue
                return _symbols_list
            except Exception as e:
                print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约账户信息(单一币种,默认usdc)
    def get_spot_account_single_asset(self, symbol='usdc'):
        for _ in range(3):
            try:
                _info = self.exchange.info.spot_user_state(
                    self.ethereum_address)['balances']
                for i in _info:
                    if i['coin'] == symbol.upper():
                        return float(i['total'])
                return 0
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取spot账户余额(多币种)
    def get_spot_account(self):
        """
        返回spot账户余额
        @return:
        """
        for _ in range(3):
            try:
                _info = self.exchange.info.spot_user_state(
                    self.ethereum_address)['balances']
                return {i['coin'].lower(): i['total'] for i in _info}
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取spot挂单信息
    def get_spot_open_order(self, symbol=None):
        """
        返回spot账户的挂单信息
        @param symbol:  btc
        @return:
        [
            {
                coin: "@142",
                side: "B",
                limitPx: "105000.0",
                sz: "0.0001",
                oid: ************,
                timestamp: *************,
                origSz: "0.0001"
            }
        ]
        """
        for _ in range(3):
            try:
                symbol_id = self.get_spot_symbol_id(symbol)
                open_order = self.exchange.info.open_orders(self.ethereum_address)
                if symbol:
                    for order in open_order:
                        if order['coin'] == symbol_id:
                            return [
                                    {
                                        'order_id': order['oid'],
                                        'symbol': symbol,
                                        'direction': 'buy' if order['side'] == 'B' else 'sell',
                                        'amount': order['sz'],
                                        'price': order['limitPx'],
                                        'order_type': 'limit',
                                        'average_price': '',
                                        'remain_amount': ''}
                                ]
                else:
                    return [
                        {
                            'order_id': i['oid'],
                            'symbol': i['coin'].lower(),
                            'direction': 'buy' if i['side'] == 'B' else 'sell',
                            'order_type': '',
                            'amount': i['origSz'],
                            'price': i['limitPx'],
                            'average_price': '',
                            'remain_amount': i['sz'],
                        } for i in open_order
                    ]
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取spot挂单订单号(默认第一个订单)
    def get_spot_open_order_id(self, symbol):
        """
        返回spot账户的挂单订单号
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                symbol_id = self.get_spot_symbol_id(symbol)
                open_order = self.exchange.info.open_orders(self.ethereum_address)
                for order in open_order:
                    if order['coin'] == symbol_id:
                        return order['oid']
                return None
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 下单
    def place_spot_order(self, symbol, direction, amount, price=float, order_type='limit'):
        """
        :param symbol:
        :param direction:
        :param amount:
        :param price:
        :param order_type:
        :return:
        """
        try:
            _direction = True if direction == 'buy' else False
            if order_type == 'limit':
                order_info = self.exchange.order(name=symbol.upper() + '/USDC', is_buy=_direction, order_type={
                                                     "limit": {"tif": "Gtc"}}, sz=float(amount), limit_px=float(price))
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
                    'symbol': symbol,
                    'direction': direction,
                    'amount': amount,
                    'price': price,
                    'order_type': order_type,
                }
            elif order_type == 'market':
                order_info = self.exchange.market_open(
                    name=symbol.upper() + '/USDC', is_buy=_direction, sz=float(amount))
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
                    'symbol': symbol,
                    'direction': direction,
                    'amount': amount,
                    'price': order_info['response']['data']['statuses'][0]['filled']['avgPx'],
                    'order_type': order_type,
                }
            else:
                return None
        except:
            print(format_exc())
            return None

    # 获取spot订单信息
    def get_spot_order_info(self, symbol, order_id, calculate_average_price=True):
        for _ in range(3):
            try:
                ntl, sz, fee, average_price = 0, 0, 0, 0
                if calculate_average_price:
                    fill_info = self.exchange.info.user_fills(
                        self.ethereum_address)
                    if fill_info:
                        if fill_info[0]['oid'] == int(order_id):
                            for i in fill_info:
                                if i['oid'] == int(order_id):
                                    ntl += float(i['px']) * float(i['sz'])
                                    sz += float(i['sz'])
                                    fee += float(i['fee'])
                            average_price = ntl / sz
                order_info = self.exchange.info.query_order_by_oid(
                    self.ethereum_address, int(order_id))['order']['order']
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_id,
                    'symbol': symbol,
                    'direction': 'buy' if order_info['side'] == 'B' else 'sell',
                    'order_type': order_info['orderType'].lower(),
                    'amount': order_info['origSz'],
                    'price': order_info['limitPx'],
                    'average_price': average_price if calculate_average_price else '',
                    'remain_amount': order_info['sz'],
                    'fee': fee,
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 取消spot挂单
    def cancel_spot_order(self, symbol, order_id):
        """
        取消spot挂单
        :param symbol:
        :param order_id:
        :return:
        """
        for _ in range(3):
            try:
                order_info = self.exchange.cancel(
                    symbol.upper() + '/USDC', int(order_id))
                return {
                    'order_id': order_id,
                    'symbol': symbol,
                    'status': 'canceled' if order_info['response']['data']['statuses'][0] == 'success' else 'failed'
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 资产划转
    def transfer_asset(self, amount, symbol='USDC', from_account='spot', to_account='usdt-swap', from_account_type='spot', to_account_type='usdt-swap'):
        """
        :param amount: 划转数量
        :param symbol: 划转的币种,默认为'USDC'
        :param from_account: 转出账户, 目前hyperliquid只有spot和usdt-swap
        :param to_account: 转入账户, 目前hyperliquid只有spot和usdt-swap
        :param from_account_type: 转出账户类型, 目前hyperliquid只有spot和usdt-swap
        :param to_account_type: 转入账户类型, 目前hyperliquid只有spot和usdt-swap
        :return: 划转结果
        """
        if from_account=='spot' and to_account=='usdt-swap':
            info = self.exchange.usd_class_transfer(amount, to_perp=True)
        elif from_account=='usdt-swap' and to_account=='spot':
            info = self.exchange.usd_class_transfer(amount, to_perp=False)
        else:
            return None
        if info['status'] == 'ok':
            return {'symbol': symbol, 'amount': amount, 'from_account': from_account, 'to_account': to_account, 'from_account_type': from_account_type, 'to_account_type': to_account_type}
        else:
            return None
        
    # endregion =============================================spot
    
    # region =============================================u swap
    # 获取最新价格
    def get_swap_latest_price(self, symbol):
        pass

    # 获取swap盘口买1价
    def get_swap_buy1(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                return float(self.exchange.info.l2_snapshot(symbol.upper())['levels'][0][0]['px'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap盘口卖1价
    def get_swap_sell1(self, symbol):
        """
        返回当前swap盘口的卖1价格
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                return float(self.exchange.info.l2_snapshot(symbol.upper())['levels'][1][0]['px'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def get_swap_best_orderbook(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60972.0', '0.85664'], 'ask': ['60973.0', '0.65602']}
        """
        for _ in range(3):
            try:
                raw_orderbook = self.exchange.info.l2_snapshot(symbol.upper())[
                    'levels']
                return {
                    'bid': [raw_orderbook[0][0]['px'], raw_orderbook[0][0]['sz']],
                    'ask': [raw_orderbook[1][0]['px'], raw_orderbook[1][0]['sz']]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def get_swap_orderbook(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              "60985.0",
              "0.42524"
            ],
            [
              "60984.0",
              "0.17214"
            ],
            ...
          ],
          "asks": [
            [
              "60986.0",
              "2.16659"
            ],
            [
              "60987.0",
              "0.96215"
            ],
            ...
          ]
        }
        """
        for _ in range(3):
            try:
                raw_orderbook = self.exchange.info.l2_snapshot(symbol.upper())[
                    'levels']
                return {
                    'symbol': symbol,
                    'bids': [[i['px'], i['sz']] for i in raw_orderbook[0][:limit]],
                    'asks': [[i['px'], i['sz']] for i in raw_orderbook[1][:limit]]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                meta_info = self.exchange.info.meta_and_asset_ctxs()
                n = self.find_index(meta_info[0]['universe'],
                                    lambda x: x['name'] == symbol.upper())
                return float(meta_info[1][n]['funding'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

        # 获取永续合约历史资金费信息

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大24(24小时)，修改下面day=1可获取更多
        :return: list越往后时间越靠近现在
        {
          "symbol": "btc",
          "funding_rate": [
            "0.0000125",
            "0.0000125",
            "0.00001366",
            "0.00002802",
            "0.0000125"
          ],
          "funding_time": [
            1723338000013,
            1723341600009,
            1723345200075,
            1723348800143,
            1723352400017
          ]
        }
        """
        for _ in range(3):
            try:
                start_time = int(
                    (datetime.now() - timedelta(days=1)).timestamp() * 1000)
                funding_rates = self.exchange.info.funding_history(
                    name=symbol.upper(), startTime=start_time)[-limit:]
                return {
                    'symbol': symbol,
                    'funding_rate': [i['fundingRate'] for i in funding_rates],
                    'funding_time': [i['time'] for i in funding_rates]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约持仓量(usdc)
    def get_swap_contract_open_interest(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                meta_info = self.exchange.info.meta_and_asset_ctxs()
                n = self.find_index(meta_info[0]['universe'],
                                    lambda x: x['name'] == symbol.upper())
                return float(meta_info[1][n]['openInterest']) * float(meta_info[1][n]['markPx'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # # TODO: 获取swap-K线
    def get_swap_kline(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info(self, symbol):
        """
        :param symbol:
        :return:
        {'maxLeverage': 20, 'name': 'SOL', 'onlyIsolated': False, 'szDecimals': 2}
        """
        for _ in range(3):
            try:
                _info = self.exchange.info.meta()['universe']
                for i in _info:
                    if i['name'] == symbol.upper():
                        return i
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                symbol_buy1 = self.get_swap_buy1(symbol)
                symbol_sell1 = self.get_swap_sell1(symbol)
                if len(str(symbol_buy1)) == len(str(symbol_sell1)):
                    return len(str(symbol_buy1).split('.')[1]) if int(str(symbol_buy1).split('.')[1]) > 0 else 0
                else:
                    return max(len(str(symbol_buy1).split('.')[1]) if int(str(symbol_buy1).split('.')[1]) > 0 else 0, len(str(symbol_sell1).split('.')[1]) if int(str(symbol_sell1).split('.')[1]) > 0 else 0)
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size(self, symbol):
        """
        :param symbol:
        :return:
        """
        return self.get_swap_instruments_info(symbol)['szDecimals']

    # 获取最小下单数量
    def get_swap_min_amount(self, symbol):
        """
        最小下单金额10u
        获取最小下单数量
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                _price = self.get_swap_buy1(symbol)
                _amount_precision = self.get_swap_order_amount_tick_size(
                    symbol)
                return ceil(10 / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            except Exception as e:
                if '429' in str(e):
                    sleep(5)
                print('当前获取数据遇到429错误,等待5秒后继续...')
        return None

    # 获取永续合约交易对(hyperliquid只有usdc合约)
    def get_swap_instruments_symbols(self, base_symbol='usdt'):
        """
        添加usdt是为了后续和其他交易所做比对，实际应该为usdc合约
        :return:
        """
        for _ in range(3):
            try:
                _symbols_list = []
                coins = [i['name']
                         for i in self.exchange.info.meta()['universe']]
                for i in coins:
                    _info = self.exchange.info.l2_snapshot(i)['levels']
                    if _info[0]:
                        _symbols_list.append(i.lower() + base_symbol)
                return _symbols_list
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约达到持仓量限制的币种名单
    def get_swap_contract_at_open_interest_cap(self):
        """
        :return:
            ["CANTO", "FTM", "GOAT", "JELLY", "LOOM", "RLB", "VVV", "ZEREBRO"]
        """
        for _ in range(3):
            try:
                return self.exchange.info.perps_at_open_interest_cap()
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None
    # 获取swap账户保证金率
    def get_swap_margin_rate(self):
        """
        获取账户保证金率
        :return:
        {
            marginSummary: {
                accountValue: "263922.709984",
                totalNtlPos: "319545.76116",
                totalRawUsd: "583468.471144",
                totalMarginUsed: "63909.152232"
            },
            crossMarginSummary: {
                accountValue: "263922.709984",
                totalNtlPos: "319545.76116",
                totalRawUsd: "583468.471144",
                totalMarginUsed: "63909.152232"
            },
            crossMaintenanceMarginUsed: "31954.576116",
            withdrawable: "200013.557752",
            assetPositions: [
                {
                type: "oneWay",
                position: {
                    coin: "POPCAT",
                    szi: "-441862.0",
                    leverage: { type: "cross", value: 5 },
                    entryPx: "0.467961",
                    positionValue: "319545.76116",
                    unrealizedPnl: "-112771.237781",
                    returnOnEquity: "-2.********",
                    liquidationPx: "1.********",
                    marginUsed: "63909.152232",
                    maxLeverage: 5,
                    cumFunding: {
                    allTime: "-75223.821197",
                    sinceOpen: "-4636.048494",
                    sinceChange: "-1119.897504"
                    }
                }
                }
            ],
            time: *************
            }
        """
        for _ in range(3):
            try:
                _info = self.exchange.info.user_state(self.ethereum_address)
                _margin_rate = float(_info['crossMaintenanceMarginUsed']) / \
                    float(_info['crossMarginSummary']['accountValue'])
                return _margin_rate
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约账户信息(单一币种,默认usdc)
    def get_swap_account_single_asset(self, symbol=None):
        for _ in range(3):
            try:
                return float(self.exchange.info.user_state(self.ethereum_address)['crossMarginSummary']['totalRawUsd'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap账户持仓信息
    def get_swap_position(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                positions = self.exchange.info.user_state(
                    self.ethereum_address)['assetPositions']
                if symbol:
                    return [
                        {
                            'symbol': symbol,
                            'direction': 'buy' if float(i['position']['szi']) > 0 else 'sell',
                            'amount': abs(float(i['position']['szi'])),
                            'price': i['position']['entryPx'],
                            'liquidation_price': i['position']['liquidationPx'],
                        } for i in positions if i['position']['coin'] == symbol.upper()
                    ]
                else:
                    return [
                        {
                            'symbol': i['position']['coin'].lower(),
                            'direction': 'buy' if float(i['position']['szi']) > 0 else 'sell',
                            'amount': abs(float(i['position']['szi'])),
                            'price': i['position']['entryPx'],
                            'liquidation_price': i['position']['liquidationPx'],
                        } for i in positions
                    ]
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap挂单信息
    def get_swap_open_order(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                open_order = self.exchange.info.open_orders(
                    self.ethereum_address)
                if symbol:
                    return [
                        {
                            'order_id': i['oid'],
                            'symbol': symbol,
                            'direction': 'buy' if i['side'] == 'B' else 'sell',
                            'order_type': '',
                            'amount': i['origSz'],
                            'price': i['limitPx'],
                            'average_price': '',
                            'remain_amount': i['sz'],
                        } for i in open_order if i['coin'] == symbol.upper()
                    ]
                else:
                    return [
                        {
                            'order_id': i['oid'],
                            'symbol': i['coin'].lower(),
                            'direction': 'buy' if i['side'] == 'B' else 'sell',
                            'order_type': '',
                            'amount': i['origSz'],
                            'price': i['limitPx'],
                            'average_price': '',
                            'remain_amount': i['sz'],
                        } for i in open_order
                    ]
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                open_order = self.get_swap_open_order(symbol)[0]
                return open_order['order_id']
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 下单
    def place_swap_order(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        try:
            _direction = True if direction == 'buy' else False
            if order_type == 'limit':
                if not close_position:
                    order_info = self.exchange.order(name=symbol.upper(), is_buy=_direction, order_type={
                                                     "limit": {"tif": "Gtc"}}, sz=float(amount), limit_px=float(price))
                else:
                    order_info = self.exchange.order(name=symbol.upper(), is_buy=_direction, order_type={
                                                     "limit": {"tif": "Gtc"}}, sz=float(amount), limit_px=float(price), reduce_only=True)
                # print(order_info)
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
                    'symbol': symbol,
                    'direction': direction,
                    'amount': amount,
                    'price': price,
                    'order_type': order_type,
                }
            elif order_type == 'market':
                if not close_position:
                    order_info = self.exchange.market_open(
                        name=symbol.upper(), is_buy=_direction, sz=float(amount))
                else:
                    order_info = self.exchange.market_close(
                        coin=symbol.upper(), sz=float(amount))
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
                    'symbol': symbol,
                    'direction': direction,
                    'amount': amount,
                    'price': order_info['response']['data']['statuses'][0]['filled']['avgPx'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['px'],
                    'order_type': order_type,
                }
        except:
            print(order_info)
            print(format_exc())
            return None

    # TODO：批量下单(不能同时下spot和swap订单)
    def place_bulk_orders(self, symbol, direction1, direction2, amount, price1=float, price2=float, close_position=False):
        try:
            order_requests = []
            # 添加现货买订单
            order_requests.append({
                "coin": '@107',     # TODO：获取现货怎么写？？？
                "is_buy": True if direction1 == 'buy' else False,
                "sz": float(amount),
                "limit_px": float(price1),
                "order_type": {"limit": {"tif": "Gtc"}},
                "reduce_only": close_position,
            })
            # 添加合约订单
            order_requests.append({
                "coin": symbol.upper(),
                "is_buy": False if direction2 == 'sell' else True,
                "sz": float(amount),
                "limit_px": float(price2),
                "order_type": {"limit": {"tif": "Gtc"}},
                "reduce_only": close_position,
            })
            order_info = self.exchange.bulk_orders(order_requests)
            return order_info
            # return {
            #     'exchange': self.exchange_name,
            #     'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
            #     'symbol': symbol,
            #     'direction': direction1,
            #     'amount': amount,
            #     'price': price1,
            #     'order_type': 'limit',
            # }
        except:
            print(order_info)
            print(format_exc())
            return None

    # 获取swap订单信息
    def get_swap_order_info(self, symbol, order_id, calculate_average_price=True):
        for _ in range(3):
            try:
                ntl, sz, fee, average_price = 0, 0, 0, 0
                if calculate_average_price:
                    fill_info = self.exchange.info.user_fills(
                        self.ethereum_address)
                    if fill_info:
                        if fill_info[0]['oid'] == order_id:
                            for i in fill_info:
                                if i['oid'] == int(order_id):
                                    ntl += float(i['px']) * float(i['sz'])
                                    sz += float(i['sz'])
                                    fee += float(i['fee'])
                            average_price = ntl / sz
                order_info = self.exchange.info.query_order_by_oid(
                    self.ethereum_address, int(order_id))['order']['order']
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_id,
                    'symbol': symbol,
                    'direction': 'buy' if order_info['side'] == 'B' else 'sell',
                    'order_type': order_info['orderType'].lower(),
                    'amount': order_info['origSz'],
                    'price': order_info['limitPx'],
                    'average_price': average_price if calculate_average_price else '',
                    'remain_amount': order_info['sz'],
                    'fee': fee,
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def cancel_swap_order(self, symbol, order_id):
        for _ in range(3):
            try:
                order_info = self.exchange.cancel(
                    symbol.upper(), int(order_id))
                return {
                    'order_id': order_id,
                    'symbol': symbol,
                    'status': 'canceled' if order_info['response']['data']['statuses'][0] == 'success' else 'failed'
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取用户的资金费率历史记录(TODO:暂未统一格式)
    def get_funding_fee(self, days=1, symbol=None, start_time=None, end_time=None):
        """
        获取资金费率历史记录
        :param days: 获取几天前到现在的数据
        :param symbol: 币种符号，如果为None则返回所有币种
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: DataFrame格式的资金费率历史记录，包含以下列:
            - time: 时间戳
            - hash: 交易哈希
            - coin: 币种
            - usdc: 资金费金额(USDC)
            - szi: 持仓数量
            - fundingRate: 资金费率
        """
        for _ in range(3):
            try:
                if start_time:
                    start_time = int(start_time.timestamp() * 1000)
                else:
                    start_time = int(
                        (datetime.now() - timedelta(days=days)).timestamp() * 1000)
                if end_time:
                    end_time = int(end_time.timestamp() * 1000)
                else:
                    end_time = int(datetime.now().timestamp() * 1000)
                _info = self.exchange.info.user_funding_history(
                    user=self.ethereum_address, startTime=start_time, endTime=end_time)

                if not _info:
                    continue
                flattened_data = []
                for record in _info:
                    flat_record = {
                        'time': record['time'],
                        'hash': record['hash'],
                        'coin': record['delta']['coin'],
                        'usdc': float(record['delta']['usdc']),
                        'szi': float(record['delta']['szi']),
                        'fundingRate': float(record['delta']['fundingRate'])
                    }
                    flattened_data.append(flat_record)
                # 转换为DataFrame
                df = pd.DataFrame(flattened_data)
                # 删除hash列
                df = df.drop(columns=['hash'])
                # 处理时间戳 转为utc+8
                df['time'] = pd.to_datetime(
                    df['time'], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
                if symbol:
                    df = df[df['coin'] == symbol.upper()]

                df = df.sort_values('time')
                df = df.reset_index(drop=True)
                return df

            except Exception as e:
                if '429' in str(e):
                    sleep(5)
                else:
                    print(format_exc())
        return None
    # endregion =====================================================swap

    # region =====================================================meme coin
    # 获取swap盘口买1价(kcoin)

    def get_swap_buy1_kcoin(self, symbol):
        """
        名称	类型	是否必需	描述
        symbol	STRING	YES	交易对
        返回当前swap盘口的买1价格(kcoin)
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                return float(self.exchange.info.l2_snapshot('k' + symbol.upper())['levels'][0][0]['px'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap盘口卖1价(kcoin)
    def get_swap_sell1_kcoin(self, symbol):
        """
        返回当前swap盘口的卖1价格(kcoin)
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                return float(self.exchange.info.l2_snapshot('k' + symbol.upper())['levels'][1][0]['px'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def get_swap_best_orderbook_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        {'bid': ['60972.0', '0.85664'], 'ask': ['60973.0', '0.65602']}
        """
        for _ in range(3):
            try:
                raw_orderbook = self.exchange.info.l2_snapshot('k' + symbol.upper())[
                    'levels']
                return {
                    'bid': [raw_orderbook[0][0]['px'], raw_orderbook[0][0]['sz']],
                    'ask': [raw_orderbook[1][0]['px'], raw_orderbook[1][0]['sz']]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    def get_swap_orderbook_kcoin(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:
        :return:
        {
          "symbol": "btc",
          "bids": [
            [
              "60985.0",
              "0.42524"
            ],
            [
              "60984.0",
              "0.17214"
            ],
            ...
          ],
          "asks": [
            [
              "60986.0",
              "2.16659"
            ],
            [
              "60987.0",
              "0.96215"
            ],
            ...
          ]
        }
        """
        for _ in range(3):
            try:
                raw_orderbook = self.exchange.info.l2_snapshot('k' + symbol.upper())[
                    'levels']
                return {
                    'symbol': symbol,
                    'bids': [[i['px'], i['sz']] for i in raw_orderbook[0][:limit]],
                    'asks': [[i['px'], i['sz']] for i in raw_orderbook[1][:limit]]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约资金费率
    def get_swap_contract_funding_rate_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                meta_info = self.exchange.info.meta_and_asset_ctxs()
                n = self.find_index(meta_info[0]['universe'],
                                    lambda x: x['name'] == 'k' + symbol.upper())
                return float(meta_info[1][n]['funding'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约历史资金费信息
    def get_swap_contract_history_funding_rate_kcoin(self, symbol, limit=5):
        """
        :param symbol:
        :param limit:默认5，最大24(24小时)，修改下面day=1可获取更多
        :return: list越往后时间越靠近现在
        {
          "symbol": "btc",
          "funding_rate": [
            "0.0000125",
            "0.0000125",
            "0.00001366",
            "0.00002802",
            "0.0000125"
          ],
          "funding_time": [
            1723338000013,
            1723341600009,
            1723345200075,
            1723348800143,
            1723352400017
          ]
        }
        """
        for _ in range(3):
            try:
                start_time = int(
                    (datetime.now() - timedelta(days=1)).timestamp() * 1000)
                funding_rates = self.exchange.info.funding_history(
                    name='k' + symbol.upper(), startTime=start_time)[-limit:]
                return {
                    'symbol': symbol,
                    'funding_rate': [i['fundingRate'] for i in funding_rates],
                    'funding_time': [i['time'] for i in funding_rates]
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取永续合约持仓量(usdc)
    def get_swap_contract_open_interest_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                meta_info = self.exchange.info.meta_and_asset_ctxs()
                n = self.find_index(meta_info[0]['universe'],
                                    lambda x: x['name'] == 'k' + symbol.upper())
                return float(meta_info[1][n]['openInterest']) * float(meta_info[1][n]['markPx'])
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # TODO: 获取swap-K线
    def get_swap_kline_kcoin(self, symbol, margin_type='usdt', period='15min', start='', end=''):
        pass

    # 获取永续合约信息 # TODO：暂未统一格式
    def get_swap_instruments_info_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        {'maxLeverage': 20, 'name': 'SOL', 'onlyIsolated': False, 'szDecimals': 2}
        """
        for _ in range(3):
            try:
                _info = self.exchange.info.meta()['universe']
                for i in _info:
                    if i['name'] == 'k' + symbol.upper():
                        return i
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取下单价格精度
    def get_swap_order_price_tick_size_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                symbol_buy1 = self.get_swap_buy1_kcoin(symbol)
                symbol_sell1 = self.get_swap_sell1_kcoin(symbol)
                if len(str(symbol_buy1)) == len(str(symbol_sell1)):
                    return len(str(symbol_buy1).split('.')[1]) if int(str(symbol_buy1).split('.')[1]) > 0 else 0
                else:
                    return max(len(str(symbol_buy1).split('.')[1]) if int(str(symbol_buy1).split('.')[1]) > 0 else 0, len(str(symbol_sell1).split('.')[1]) if int(str(symbol_sell1).split('.')[1]) > 0 else 0)
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取下单数量精度
    def get_swap_order_amount_tick_size_kcoin(self, symbol):
        """
        :param symbol:
        :return:
        """
        return self.get_swap_instruments_info_kcoin(symbol)['szDecimals']

    # 获取最小下单数量
    def get_swap_min_amount_kcoin(self, symbol):
        """
        最小下单金额10u
        获取最小下单数量
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                _price = self.get_swap_buy1_kcoin(symbol)
                _amount_precision = self.get_swap_order_amount_tick_size_kcoin(
                    symbol)
                return ceil(10 / _price * 10 ** _amount_precision) / 10 ** _amount_precision
            except Exception as e:
                if '429' in str(e):
                    sleep(5)
                print('当前获取数据遇到429错误,等待5秒后继续...')
        return None

    # 获取swap账户持仓信息
    def get_swap_position_kcoin(self, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                positions = self.exchange.info.user_state(
                    self.ethereum_address)['assetPositions']
                if symbol:
                    return [
                        {
                            'symbol': 'k' + symbol,
                            'direction': 'buy' if float(i['position']['szi']) > 0 else 'sell',
                            'amount': abs(float(i['position']['szi'])),
                            'price': i['position']['entryPx'],
                            'liquidation_price': i['position']['liquidationPx'],
                        } for i in positions if i['position']['coin'] == 'k' + symbol.upper()
                    ]
                else:
                    return [
                        {
                            'symbol': i['position']['coin'].lower(),
                            'direction': 'buy' if float(i['position']['szi']) > 0 else 'sell',
                            'amount': abs(float(i['position']['szi'])),
                            'price': i['position']['entryPx'],
                            'liquidation_price': i['position']['liquidationPx'],
                        } for i in positions
                    ]
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap挂单信息
    def get_swap_open_order_kcoin(self, symbol=None):
        """
        返回swap账户的挂单信息
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                open_order = self.exchange.info.open_orders(
                    self.ethereum_address)
                if symbol:
                    return [
                        {
                            'order_id': i['oid'],
                            'symbol': 'k' + symbol,
                            'direction': 'buy' if i['side'] == 'B' else 'sell',
                            'order_type': '',
                            'amount': i['origSz'],
                            'price': i['limitPx'],
                            'average_price': '',
                            'remain_amount': i['sz'],
                        } for i in open_order if i['coin'] == 'k' + symbol.upper()
                    ]
                else:
                    return [
                        {
                            'order_id': i['oid'],
                            'symbol': i['coin'].lower(),
                            'direction': 'buy' if i['side'] == 'B' else 'sell',
                            'order_type': '',
                            'amount': i['origSz'],
                            'price': i['limitPx'],
                            'average_price': '',
                            'remain_amount': i['sz'],
                        } for i in open_order
                    ]
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 获取swap挂单订单号(默认第一个订单)
    def get_swap_open_order_id_kcoin(self, symbol):
        """
        返回swap账户的挂单订单号
        :param symbol:
        :return:
        """
        for _ in range(3):
            try:
                open_order = self.get_swap_open_order_kcoin(symbol)[0]
                return open_order['order_id']
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 下单(kcoin)
    def place_swap_order_kcoin(self, symbol, direction, amount, price=float, order_type='limit', close_position=False):
        try:
            _direction = True if direction == 'buy' else False
            if order_type == 'limit':
                if not close_position:
                    order_info = self.exchange.order(name='k' + symbol.upper(), is_buy=_direction, order_type={
                                                     "limit": {"tif": "Gtc"}}, sz=float(amount), limit_px=float(price))
                else:
                    order_info = self.exchange.order(name='k' + symbol.upper(), is_buy=_direction, order_type={
                                                     "limit": {"tif": "Gtc"}}, sz=float(amount), limit_px=float(price), reduce_only=True)
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
                    'symbol': 'k' + symbol,
                    'direction': direction,
                    'amount': amount,
                    'price': price,
                    'order_type': order_type,
                }
            elif order_type == 'market':
                if not close_position:
                    order_info = self.exchange.market_open(
                        name='k' + symbol.upper(), is_buy=_direction, sz=float(amount))
                else:
                    order_info = self.exchange.market_close(
                        name='k' + symbol.upper(), sz=float(amount))
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_info['response']['data']['statuses'][0]['filled']['oid'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['oid'],
                    'symbol': 'k' + symbol,
                    'direction': direction,
                    'amount': amount,
                    'price': order_info['response']['data']['statuses'][0]['filled']['avgPx'] if 'filled' in str(order_info) else order_info['response']['data']['statuses'][0]['resting']['px'],
                    'order_type': order_type,
                }
        except:
            print(format_exc())
            return None

    # 获取订单信息（kcoin）
    def get_swap_order_info_kcoin(self, symbol, order_id, calculate_average_price=True):
        for _ in range(3):
            try:
                ntl, sz, fee, average_price = 0, 0, 0, 0
                if calculate_average_price:
                    fill_info = self.exchange.info.user_fills(
                        self.ethereum_address)
                    if fill_info:
                        if fill_info[0]['oid'] == order_id:
                            for i in fill_info:
                                if i['oid'] == int(order_id):
                                    ntl += float(i['px']) * float(i['sz'])
                                    sz += float(i['sz'])
                                    fee += float(i['fee'])
                            average_price = ntl / sz
                order_info = self.exchange.info.query_order_by_oid(
                    self.ethereum_address, int(order_id))['order']['order']
                return {
                    'exchange': self.exchange_name,
                    'order_id': order_id,
                    'symbol': 'k' + symbol,
                    'direction': 'buy' if order_info['side'] == 'B' else 'sell',
                    'order_type': order_info['orderType'].lower(),
                    'amount': order_info['origSz'],
                    'price': order_info['limitPx'],
                    'average_price': average_price if calculate_average_price else '',
                    'remain_amount': order_info['sz'],  # TODO：sz是否是剩余数量？
                    'fee': fee,
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None

    # 取消订单(kcoin)
    def cancel_swap_order_kcoin(self, symbol, order_id):
        for _ in range(3):
            try:
                order_info = self.exchange.cancel(
                    'k' + symbol.upper(), int(order_id))
                return {
                    'order_id': order_id,
                    'symbol': 'k' + symbol,
                    'status': 'canceled' if order_info['response']['data']['statuses'][0] == 'success' else 'failed'
                }
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None
    # endregion =====================================================meme coin

    # region =====================================================获取指定地址的用户信息
    # 获取指定地址的持仓信息
    def get_swap_position_by_user_address(self, user_address, symbol=None):
        """
        返回当前账户的持仓信息
        @param symbol:  btc
        @return:
        """
        for _ in range(3):
            try:
                positions = self.exchange.info.user_state(
                    user_address)['assetPositions']
                if positions:
                    return positions
                else:
                    return None
            except Exception as e:
                # print(format_exc())
                if '429' in str(e):
                    sleep(5)
                else:
                    return None


    # endregion =====================================================函数
